model Address {
    id               String     @id @default(auto()) @map("_id") @db.ObjectId
    street           String?
    city             String?
    state            String?
    country          String?
    latitude         Float?
    longitude        Float?
    label            String?
    isDefault        Boolean    @default(false)
    user             User?      @relation("UserAddress", fields: [userId], references: [id], onDelete: Cascade)
    userId           String?    @db.ObjectId
    deliveryDropoffs Delivery[] @relation("DeliveryDropoff")
    createdAt        DateTime   @default(now())
    updatedAt        DateTime   @updatedAt
}
