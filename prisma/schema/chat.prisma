model Chat {
    id        String     @id @default(auto()) @map("_id") @db.ObjectId
    user      User       @relation(fields: [userId], references: [id])
    userId    String     @db.ObjectId
    status    ChatStatus @default(OPEN)
    title     String
    messages  Message[]
    createdAt DateTime   @default(now())
    updatedAt DateTime   @updatedAt
}

model Message {
    id        String   @id @default(auto()) @map("_id") @db.ObjectId
    chat      Chat     @relation(fields: [chatId], references: [id])
    chatId    String   @db.ObjectId
    sender    User     @relation(fields: [senderId], references: [id])
    senderId  String   @db.ObjectId
    content   String
    isRead    <PERSON>an  @default(false)
    createdAt DateTime @default(now())
}
