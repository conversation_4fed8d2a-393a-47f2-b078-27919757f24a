model Delivery {
    id                String          @id @default(auto()) @map("_id") @db.ObjectId
    currentDriver     DriverDetails?  @relation("AssignedErrand", fields: [currentDriverId], references: [id], onDelete: Cascade)
    currentDriverId   String?         @db.ObjectId
    completedBy       DriverDetails?  @relation("CompletedErrands", fields: [completedById], references: [id])
    completedById     String?         @db.ObjectId
    status            ErrandStatus    @default(PENDING)
    dropoffAddress    Address         @relation("DeliveryDropoff", fields: [dropoffAddressId], references: [id])
    dropoffAddressId  String          @db.ObjectId
    pickupAddresses   String[]
    currentLong       Float?
    currentLat        Float?
    estimatedDuration Int?
    estimatedDistance Float?
    actualDuration    Int?
    actualDistance    Float?
    startTime         DateTime?
    order             Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
    orderId           String          @unique @db.ObjectId
    endTime           DateTime?
    statusHistory     StatusHistory[]
    fee               Float?
    createdAt         DateTime        @default(now())
    updatedAt         DateTime        @updatedAt
}

model StatusHistory {
    id         String       @id @default(auto()) @map("_id") @db.ObjectId
    delivery   Delivery     @relation(fields: [deliveryId], references: [id], onDelete: Cascade)
    deliveryId String       @db.ObjectId
    status     ErrandStatus
    notes      String?
    createdAt  DateTime     @default(now())
}
