model Discount {
    id             String       @id @default(auto()) @map("_id") @db.ObjectId
    code           String       @unique
    type           DiscountType
    value          Float
    description    String?
    minOrderAmount Float?
    maxDiscount    Float?
    startDate      DateTime
    endDate        DateTime
    isActive       Boolean      @default(true)
    usageLimit     Int?
    usageCount     Int          @default(0)
    orders         Order[]
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt
}
