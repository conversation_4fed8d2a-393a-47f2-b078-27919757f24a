model DriverDetails {
    id               String       @id @default(auto()) @map("_id") @db.ObjectId
    user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId           String       @unique @db.ObjectId
    licenseNumber    String       @unique
    vehicleType      VehicleType
    vehicleModel     String?
    isAvailable      Boolean      @default(true)
    currentStatus    DriverStatus @default(OFFLINE)
    completedErrands Delivery[]   @relation("CompletedErrands")
    assignedErrands  Delivery[]   @relation("AssignedErrand")
    createdAt        DateTime     @default(now())
    updatedAt        DateTime     @updatedAt
    review           Review[]
}
