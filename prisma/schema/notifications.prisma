model Notification {
    id        String    @id @default(auto()) @map("_id") @db.ObjectId
    title     String
    body      String
    data      Json?
    isRead    Bo<PERSON>an   @default(false)
    achieved  Boolean   @default(false)
    createdAt DateTime  @default(now())
    sentAt    DateTime?
    userId    String    @db.ObjectId
    user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}
