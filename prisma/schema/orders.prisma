model Order {
    id              String      @id @default(auto()) @map("_id") @db.ObjectId
    customer        User        @relation("CustomerOrders", fields: [customerId], references: [id], onDelete: Cascade)
    customerId      String      @db.ObjectId
    orderPreference String
    status          OrderStatus
    items           OrderItem[]
    delivery        Delivery?
    totalAmount     Float
    createdAt       DateTime    @default(now())
    updatedAt       DateTime    @updatedAt
    payment         Payment?
    isPayed         Boolean     @default(false)
    discount        Discount?   @relation(fields: [discountId], references: [id])
    discountId      String?     @db.ObjectId
    discountAmount  Float?
}

model OrderItem {
    id        String  @id @default(auto()) @map("_id") @db.ObjectId
    order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
    orderId   String  @db.ObjectId
    product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
    productId String  @db.ObjectId
    quantity  Int
    price     Float
}
