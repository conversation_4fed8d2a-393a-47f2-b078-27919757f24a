model Payment {
    id              String          @id @default(auto()) @map("_id") @db.ObjectId
    paymentId       String          @unique
    amount          Float
    currency        String
    gateway         PaymentGateways
    phoneNumber     String
    description     String
    status          String
    channel         String?
    transactionUUID String?
    paymentUrl      String?
    ref             String?
    mobileOperator  String?
    userEmail       String?
    refundAmount    Float?
    refundReason    String?
    retryCount      Int             @default(0)
    failureReason   String?
    createdAt       DateTime        @default(now())
    lastRetryAt     DateTime?
    user            User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId          String?         @db.ObjectId
    order           Order?          @relation(fields: [orderId], references: [id], onDelete: Cascade)
    orderId         String          @unique @db.ObjectId
}
