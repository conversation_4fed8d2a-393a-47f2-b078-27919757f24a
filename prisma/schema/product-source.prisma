model ProductSource {
    id                      String                 @id @default(auto()) @map("_id") @db.ObjectId
    name                    String
    rating                  Float
    placeId                 String                 @unique
    description             String?
    totalRating             Float
    products                Product[]
    createdAt               DateTime               @default(now())
    updatedAt               DateTime               @updatedAt
    images                  String[]
    ProductSourceCategory   ProductSourceCategory? @relation(fields: [productSourceCategoryId], references: [id])
    productSourceCategoryId String?                @db.ObjectId
    lat                     Float?
    long                    Float?
    street                  String?
    city                    String?
    state                   String?
    country                 String?
    radius                  Float?
}
