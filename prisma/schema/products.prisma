model Product {
    id              String           @id @default(auto()) @map("_id") @db.ObjectId
    name            String
    images          String[]
    description     String?
    deliveryPrice   Float
    price           Float
    category        ProductCategory? @relation(fields: [categoryId], references: [id], onDelete: Cascade)
    categoryId      String?          @db.ObjectId
    orderItems      OrderItem[]
    createdAt       DateTime         @default(now())
    updatedAt       DateTime         @updatedAt
    productSource   ProductSource?   @relation(fields: [productSourceId], references: [id], onDelete: Cascade)
    productSourceId String?          @db.ObjectId
}
