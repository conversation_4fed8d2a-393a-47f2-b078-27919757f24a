model Review {
    id              String         @id @default(auto()) @map("_id") @db.ObjectId
    rating          Int
    comment         String?
    reviewer        User?          @relation(fields: [reviewerId], references: [id], onDelete: Cascade)
    reviewerId      String?        @db.ObjectId
    createdAt       DateTime       @default(now())
    updatedAt       DateTime       @updatedAt
    driverDetails   DriverDetails? @relation(fields: [driverDetailsId], references: [id], onDelete: Cascade)
    driverDetailsId String?        @db.ObjectId
}
