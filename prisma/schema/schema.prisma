datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["prismaSchemaFolder"]
}

enum UserRole {
  CUSTOMER
  DRIVER
  ADMIN
}

model AdminDetails {
  id          String            @id @default(auto()) @map("_id") @db.ObjectId
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String            @unique @db.ObjectId
  permissions AdminPermission[]
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

enum OrderStatus {
  PENDING
  ASSIGNED
  ACCEPTED
  PENDING_PAYMENT
  IN_PROGRESS
  DELIVERED
  CANCELLED
}

enum ErrandStatus {
  PENDING
  ACCEPTED
  ASSIGNED
  PICKED_UP
  STARTED
  PENDING_PAYMENT
  IN_TRANSIT
  ARRIVED_AT_PICKUP
  ARRIVED_AT_DROPOFF
  COMPLETED
  CANCELLED
  FAILED
}

enum DriverStatus {
  OFFLINE
  AVAILABLE
  BUSY
  PENDING_DELIVERY
  ON_ERRAND
  UNAVAILABLE
  ON_BREAK
}

enum VehicleType {
  BICYCLE
  MOTORCYCLE
  CAR
  VAN
  TRUCK
}

enum AdminPermission {
  MANAGE_USERS
  MANAGE_ORDERS
  MANAGE_PRODUCTS
  MANAGE_CATEGORIES
  MANAGE_SETTINGS
  MANAGE_NOTIFICATIONS
  MANAGE_CHAT
  MANAGE_ADMINS
  MANAGE_STORES
  MANAGE_DRIVERS
}

enum OTPType {
  EMAIL_VERIFICATION
  PHONE_VERIFICATION
  PASSWORD_RESET
  ACCOUNT_DELETION
  LOGIN
}

enum PaymentGateways {
  FAPSHI
  MONETBILL
  CAMPAY
  PAYPAL
  STRIPE
}

enum NotificationState {
  ACTIVE
  ACCEPTED
  DISABLE
}

enum ChatStatus {
  OPEN
  CLOSED
  RESOLVED
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}
