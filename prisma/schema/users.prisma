model User {
    id              String         @id @default(auto()) @map("_id") @db.ObjectId
    email           String?        @unique
    fullname        String
    password        String?
    googleId        String?
    role            UserRole
    isEmailVerified Boolean        @default(false)
    isPhoneVerified Boolean        @default(false)
    phone           String?
    isVerified      Boolean        @default(false)
    profile         Profile?
    isDeleted       Boolean        @default(false)
    deletedAt       DateTime?
    orders          Order[]        @relation("CustomerOrders")
    driverDetails   DriverDetails?
    adminDetails    AdminDetails?
    fcmToken        String?
    expoPushToken   String?
    otps            OTP[]
    createdAt       DateTime       @default(now())
    updatedAt       DateTime       @updatedAt
    address         Address[]      @relation("UserAddress")
    notification    Notification[]
    payments        Payment[]
    chat            Chat[]
    message         Message[]
    review          Review[]

    @@index([email, phone])
}
