import { Module, MiddlewareConsumer } from '@nestjs/common';
import { ModulesModule } from './modules/modules.module';
import { ConfigModule } from '@nestjs/config';
import { CoreModule } from './core/core.module';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { RequestLoggerMiddleware } from './common/middleware/logger.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `./.env.${process.env.NODE_ENV}` || './.env',
    }),
    ModulesModule,
    CoreModule,
  ],

  controllers: [HealthController],

  providers: [HealthService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }
}
