import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export class Filter {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  field?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  value?: string;
}

export class PaginationDto {
  @ApiProperty({ required: false, default: 1 })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({ required: false, default: 50 })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit: number = 50;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({ required: false, enum: SortOrder })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;
}
