import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';

@Injectable()
export class IsVerifiedGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not found.');
    }

    // Allow access if either email or phone is verified
    if (!user.isEmailVerified && !user.isPhoneVerified) {
      throw new ForbiddenException(
        'Please verify either your email or phone number to continue.',
      );
    }

    return true;
  }
}
