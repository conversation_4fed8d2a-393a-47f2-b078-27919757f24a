import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Socket } from 'socket.io';
import { verify } from 'jsonwebtoken';
import { WsException } from '@nestjs/websockets';

@Injectable()
export class WebsocketGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const type = context.getType();
    if (type !== 'ws') return true;

    const client: Socket = context.switchToWs().getClient();

    return this.validateToken(client);
  }

  private async validateToken(client: Socket): Promise<boolean> {
    const auth_token = client.handshake.auth.token
      ? client.handshake.auth.token
      : client.handshake.headers['authorization'];

    if (!auth_token) {
      throw new WsException('No authentication token provided');
    }

    try {
      const payload = verify(auth_token, process.env.JWT_SECRET || '');

      client.data.user = payload;

      return true;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new WsException('Token has expired');
      }

      throw new WsException('Invalid authentication token');
    }
  }
}
