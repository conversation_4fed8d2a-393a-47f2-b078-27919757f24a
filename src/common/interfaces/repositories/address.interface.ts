import { Address, NewAddress } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IAddressRepository {
  create(data: NewAddress): Promise<Address>;
  update(id: string, data: any): Promise<Address>;
  findOne(id: string): Promise<Address>;
  getAllUsersAddress(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Address>>;
  findAddressInDelivery(id: string): Promise<boolean>;
  updateMany(data: Partial<NewAddress>, userId: string, id?: string);
  findAll(userId: string): Promise<Address[]>;
  setDefault(id: string, userId: string): Promise<Address>;
  remove(id: string): Promise<void>;
}
