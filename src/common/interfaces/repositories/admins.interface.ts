import { Admin, NewAdmin } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IAdminsRepository {
  create(data: NewAdmin): Promise<Admin>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Admin>>;
  findOne(id: string): Promise<Admin>;
  update(id: string, data: any): Promise<Admin>;
  remove(id: string): Promise<void>;
}
