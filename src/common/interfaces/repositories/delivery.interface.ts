import { ErrandStatus } from '@prisma/client';
import { Delivery, NewDelivery } from 'src/common/schemas';
import { PaginationParams, PaginatedResult } from '../pagination.interface';

export interface IDeliveryRepository {
  create(data: NewDelivery): Promise<Delivery>;
  findOne(id: string): Promise<Delivery>;
  update(id: string, data: Partial<NewDelivery>): Promise<Delivery>;
  updateStatus(id: string, status: ErrandStatus): Promise<Delivery>;
  updateCurrentDriver(id: string, driverId: string): Promise<Delivery>;
  getDriverDailyDeliveries(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Delivery>>;
  updateCurrentLocation(
    id: string,
    latitude: number,
    longitude: number,
  ): Promise<Delivery>;

  findAll(
    params: PaginationParams,
    status: ErrandStatus,
  ): Promise<PaginatedResult<Delivery>>;
  findByDriver(
    driverId: string,
    params: PaginationParams,
  ): Promise<PaginatedResult<Delivery>>;

  findByUser(
    customer: string,
    params: PaginationParams,
  ): Promise<PaginatedResult<Delivery>>;

  remove(id: string): Promise<void>;
}
