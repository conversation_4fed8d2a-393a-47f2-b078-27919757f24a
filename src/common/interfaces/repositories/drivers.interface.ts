import { Driver, NewDriver } from 'src/common/schemas';
import { PaginationParams, PaginatedResult } from '../pagination.interface';

export interface IDriversRepository {
  create(data: NewDriver): Promise<Driver>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Driver>>;
  findByLinsenceNumber(licenseNumber: string): Promise<Driver>;
  findAvailableDrivers(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Driver>>;
  findByUserId(userId: string): Promise<Driver>;
  findOne(id: string): Promise<Driver>;
  update(id: string, data: any): Promise<Driver>;
  remove(id: string): Promise<void>;
}
