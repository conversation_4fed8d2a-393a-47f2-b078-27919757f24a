import { Notification } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface INotificationsRepository {
  create(data: any): Promise<Notification>;
  findOne(id: string): Promise<Notification>;
  remove(id: string): Promise<void>;
  markAllAsRead(userId: string): Promise<void>;
  markAsRead(userId: string, notificationId: string): Promise<void>;
  count(where: any): Promise<number>;
  deleteAllNotifications(userId: string): Promise<void>;

  createManyWithUserId(
    userId: string[],
    notifications: {
      title: string;
      body: string;
      data?: Record<string, any>;
      isRead?: boolean;
      sentAt?: Date;
    },
  ): Promise<Notification[]>;

  findAllByUser(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Notification>>;
}
