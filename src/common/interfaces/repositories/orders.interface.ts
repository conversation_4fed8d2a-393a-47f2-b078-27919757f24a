import { NewOrder, Orders } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';
import { OrderStatus } from '@prisma/client';

export interface IOrdersRepository {
  create(data: NewOrder): Promise<Orders>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Orders>>;
  findByCustomer(
    customerId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>>;

  findOrderByStatus(
    userId: string,
    status: OrderStatus,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>>;

  findByDriver(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>>;

  findWithFilters(
    pagination: PaginationParams,
    startDate?: Date,
    endDate?: Date,
    status?: OrderStatus,
    customerId?: string,
  ): Promise<PaginatedResult<Orders>>;

  getUserOrderCount(userId: string): Promise<number>;

  getOrderStats(
    startDate: Date,
    endDate: Date,
  ): Promise<{
    totalOrders: number;
    ordersByStatus: { status: OrderStatus; _count: { id: number } }[];
    revenue: number;
    avgOrderValue: number;
    ordersPerDay: { date: string; count: number }[];
  }>;

  getDailyOrders(date: string): Promise<any>;
  findOne(id: string): Promise<Orders>;
  update(id: string, data: Partial<NewOrder>): Promise<Orders>;
  remove(id: string): Promise<void>;
}
