import { NewPaymentAccount, PaymentAccount } from 'src/common/schemas';

export interface IPaymentAccountsRepository {
  create(data: NewPaymentAccount): Promise<PaymentAccount>;

  findOne(id: string): Promise<PaymentAccount>;
  findByNumber(number: string): Promise<PaymentAccount>;
  findByUserId(userId: string): Promise<PaymentAccount[]>;
  findAll(): Promise<PaymentAccount[]>;
  update(id: string, data: any): Promise<PaymentAccount>;
  remove(id: string): Promise<void>;
}
