import { NewPayment, Payment, UpdatePayment } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';
import { PaymentGateways } from '@prisma/client';

export interface IPaymentRepository {
  create(data: NewPayment): Promise<Payment>;
  findOne(id: string): Promise<Payment>;
  findByOrderId(orderId: string): Promise<Payment>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Payment>>;
  findByPaymentId(paymentId: string): Promise<Payment>;
  findByGateway(paymentId: string, gateway: PaymentGateways): Promise<Payment>;
  updateByPaymentId(paymentId: string, data: UpdatePayment): Promise<Payment>;
  findByUserId(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Payment>>;
  update(id: string, data: any): Promise<Payment>;
  getStatistics(startDate: Date, endDate: Date): Promise<any>;
  getTotalAmount(): Promise<any>;
  remove(id: string): Promise<void>;
}
