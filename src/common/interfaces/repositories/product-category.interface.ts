import { NewProductCategory, ProductCategory } from 'src/common/schemas';

export interface IProductCategoryRepository {
  create(data: NewProductCategory): Promise<ProductCategory>;
  findOne(id: string): Promise<ProductCategory>;
  findByName(name: string): Promise<ProductCategory>;
  update(
    id: string,
    data: Partial<NewProductCategory>,
  ): Promise<ProductCategory>;
  findAll(): Promise<ProductCategory[]>;
  remove(id: string): Promise<void>;
}
