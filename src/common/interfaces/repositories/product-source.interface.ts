import { NewProductSource, ProductSource } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IProductSourceRepository {
  create(data: NewProductSource): Promise<ProductSource>;
  createBulk(data: NewProductSource[]): Promise<any[]>;
  findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<ProductSource>>;
  search(data: any): Promise<any>;
  findOne(id: string): Promise<ProductSource>;
  findByPlacesId(placeId: string): Promise<ProductSource>;
  findByPlacesIds(placeIds: string[]): Promise<ProductSource[]>;
  update(id: string, data: Partial<NewProductSource>): Promise<ProductSource>;
  remove(id: string): Promise<void>;
  bulkDelete(ids: string[]): Promise<void>;
}
