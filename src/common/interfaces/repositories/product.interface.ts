import { NewProduct, Products } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IProductRepository {
  create(data: NewProduct): Promise<Products>;
  createBulk(data: NewProduct[]): Promise<any>;
  findMany(ids: string[]): Promise<Products[]>;
  getBySourceCategory(
    id: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Products>>;
  getByCategory(id: string, pagination: PaginationParams): Promise<Products[]>;
  getProductByProductSource(
    id: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Products>>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Products>>;
  search(data: any): Promise<any>;

  findOne(id: string): Promise<Products>;
  update(id: string, data: Partial<NewProduct>): Promise<Products>;
  remove(id: string): Promise<void>;
}
