import { NewReview, Review } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';

export interface IReviewsRepository {
  create(data: NewReview): Promise<Review>;
  findOne(id: string): Promise<Review>;
  remove(id: string): Promise<void>;
  findByUser(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Review>>;

  findByDriver(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Review>>;

  findAll(pagination: PaginationParams): Promise<PaginatedResult<Review>>;
}
