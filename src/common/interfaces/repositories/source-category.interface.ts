import { NewSourceCategory, SourceCategory } from 'src/common/schemas';

export interface ISourceCategoryRepository {
  create(data: NewSourceCategory): Promise<SourceCategory>;
  findOne(id: string): Promise<SourceCategory>;
  findByName(name: string): Promise<SourceCategory>;
  update(id: string, data: Partial<NewSourceCategory>): Promise<SourceCategory>;
  findAll(): Promise<SourceCategory[]>;
  remove(id: string): Promise<void>;
}
