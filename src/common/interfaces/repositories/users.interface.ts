import { Users } from 'src/common/schemas';
import { PaginatedResult, PaginationParams } from '../pagination.interface';
import { User } from '@prisma/client';

export interface IUsersRepository {
  create(data: any): Promise<Users>;
  findAll(pagination: PaginationParams): Promise<PaginatedResult<Users>>;
  findOne(id: string): Promise<Users>;
  getUserByEmail(email: string): Promise<Users>;
  getUserByIdentifier(identifier: string): Promise<User>;
  getUserWithPassword(email: string): Promise<User>;
  getUserByPhone(phone: string): Promise<Users>;

  findManyUsersById(ids: string[]): Promise<Users[]>;

  getTrashedAccounts(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>>;

  findAllDrivers(pagination: PaginationParams): Promise<PaginatedResult<Users>>;
  findAllCustomers(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>>;

  getAdmins(): Promise<Users[]>;

  findAllAdmins(pagination: PaginationParams): Promise<PaginatedResult<Users>>;

  addGoogleUser(data: any): Promise<Users>;
  updateFirebaseToken(id: string, data: Partial<Users>): Promise<Users>;
  updateExpoToken(id: string, data: any): Promise<Users>;
  softDelete(id: string): Promise<void>;
  restore(id: string): Promise<Users>;
  update(id: string, data: any): Promise<Users>;
  remove(id: string): Promise<void>;
}
