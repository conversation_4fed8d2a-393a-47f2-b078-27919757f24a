import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestLoggerMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';
    const startTime = Date.now();

    // Generate unique request ID for tracing
    const requestId = this.generateRequestId();
    req['requestId'] = requestId;

    this.logger.log(
      `[${requestId}] ${method} ${originalUrl} - ${ip} - ${userAgent}`,
    );

    // Capture response details
    const originalJson = res.json;
    const originalSend = res.send;

    res.json = function (body: any) {
      res.locals.body = body;
      return originalJson.call(this, body);
    };

    res.send = function (body: any) {
      res.locals.body = body;
      return originalSend.call(this, body);
    };

    res.on('finish', () => {
      const { statusCode } = res;
      const contentLength = res.get('content-length') || 0;
      const responseTime = Date.now() - startTime;

      const logLevel = statusCode >= 400 ? 'error' : 'log';

      this.logger[logLevel](
        `[${requestId}] ${method} ${originalUrl} - ${statusCode} - ${contentLength}bytes - ${responseTime}ms`,
      );

      // Log error responses
      if (statusCode >= 400 && res.locals.body) {
        this.logger.error(
          `[${requestId}] Error Response: ${JSON.stringify(res.locals.body)}`,
        );
      }
    });

    next();
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}
