import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class IdValidationPipe implements PipeTransform {
  transform(value: any) {
    // Check if the ID is a string
    if (!value || typeof value !== 'string') {
      throw new BadRequestException('Invalid ID: must be a string');
    }

    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    if (!objectIdRegex.test(value)) {
      throw new BadRequestException(
        'Invalid ID: must be a valid MongoDB ObjectId',
      );
    }

    return value;
  }
}
