import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IAddressRepository } from '../interfaces';
import { Address, NewAddress } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class AddressRepository implements IAddressRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewAddress): Promise<Address> {
    return this.prisma.address.create({ data });
  }

  async findAddressInDelivery(id: string) {
    const deliveryWithAddress = await this.prisma.delivery.findMany({
      where: {
        dropoffAddressId: id,
        status: {
          notIn: ['COMPLETED', 'CANCELLED', 'PENDING'],
        },
      },
    });
    return deliveryWithAddress.length > 0;
  }

  async updateMany(data: Partial<NewAddress>, userId: string, id?: string) {
    return this.prisma.address.updateMany({
      where: { userId, isDefault: true, id: { not: id } },
      data,
    });
  }
  async update(id: string, data: any): Promise<Address> {
    return this.prisma.address.update({ where: { id }, data });
  }

  async findOne(id: string): Promise<Address> {
    return this.prisma.address.findUnique({ where: { id } });
  }

  async getAllUsersAddress(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Address>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.address.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              fullname: true,
              email: true,
            },
          },
        },
      }),
      this.prisma.address.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAll(userId: string): Promise<Address[]> {
    return this.prisma.address.findMany({
      where: { userId },
      orderBy: [{ isDefault: 'desc' }, { createdAt: 'desc' }],
    });
  }

  async setDefault(id: string, userId: string): Promise<Address> {
    await this.prisma.address.updateMany({
      where: {
        userId,
        isDefault: true,
        id: { not: id },
      },
      data: { isDefault: false },
    });

    return this.prisma.address.update({
      where: { id },
      data: { isDefault: true },
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.address.delete({ where: { id } });
  }
}
