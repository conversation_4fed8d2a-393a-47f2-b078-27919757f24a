import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IAdminsRepository } from '../interfaces';
import { Admin, NewAdmin, AdminSelect } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class AdminsRepository implements IAdminsRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewAdmin): Promise<Admin> {
    return this.prisma.adminDetails.create({
      data,
      select: AdminSelect,
    });
  }

  async findAll(pagination: PaginationParams): Promise<PaginatedResult<Admin>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.adminDetails.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: AdminSelect,
      }),
      this.prisma.adminDetails.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<Admin> {
    return this.prisma.adminDetails.findUnique({
      where: { id },
      select: AdminSelect,
    });
  }

  async update(id: string, data: any): Promise<Admin> {
    return this.prisma.adminDetails.update({
      where: { id },
      data,
      select: AdminSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.adminDetails.delete({ where: { id } });
  }
}
