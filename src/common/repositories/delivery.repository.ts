import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IDeliveryRepository } from '../interfaces';
import {
  Delivery,
  DeliverySelect,
  NewDelivery,
  UpdateDelivery,
} from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';
import { ErrandStatus } from '@prisma/client';

@Injectable()
export class DeliveryRepository implements IDeliveryRepository {
  constructor(private readonly prisma: PrismaService) {}

  async getDriverDailyDeliveries(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Delivery>> {
    const { page, limit } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.delivery.findMany({
        where: {
          currentDriver: {
            userId: driverId,
          },
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lte: new Date(),
          },
        },
        skip,
        take: limit,
        select: DeliverySelect,
      }),
      this.prisma.delivery.count({
        where: {
          currentDriver: { userId: driverId },
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
            lte: new Date(),
          },
        },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async remove(id: string): Promise<void> {
    await this.prisma.delivery.delete({ where: { id } });
  }
  async create(data: NewDelivery): Promise<Delivery> {
    return this.prisma.delivery.create({ data, select: DeliverySelect });
  }

  async findOne(id: string): Promise<Delivery> {
    return this.prisma.delivery.findUnique({
      where: { id },
      select: DeliverySelect,
    });
  }

  async update(id: string, data: UpdateDelivery): Promise<Delivery> {
    return this.prisma.delivery.update({
      where: { id },
      data,
      select: DeliverySelect,
    });
  }

  async updateStatus(id: string, status: ErrandStatus): Promise<Delivery> {
    return this.prisma.delivery.update({
      where: { id },
      data: { status },
      select: DeliverySelect,
    });
  }

  async updateCurrentDriver(id: string, driverId: string): Promise<Delivery> {
    return this.prisma.delivery.update({
      where: { id },
      data: { currentDriverId: driverId },
      select: DeliverySelect,
    });
  }

  async updateCurrentLocation(
    id: string,
    latitude: number,
    longitude: number,
  ): Promise<Delivery> {
    return this.prisma.delivery.update({
      where: { id },
      data: { currentLat: latitude, currentLong: longitude },
      select: DeliverySelect,
    });
  }

  async findAll(
    pagination: PaginationParams,
    status?: ErrandStatus,
  ): Promise<PaginatedResult<Delivery>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.delivery.findMany({
        where: { status },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DeliverySelect,
      }),
      this.prisma.delivery.count({ where: { status } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByDriver(
    driverId: string,
    pagination: PaginationParams,
    status?: ErrandStatus,
  ): Promise<PaginatedResult<Delivery>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.delivery.findMany({
        where: { currentDriver: { userId: driverId }, status },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DeliverySelect,
      }),
      this.prisma.delivery.count({
        where: { currentDriver: { userId: driverId } },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByUser(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Delivery>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.delivery.findMany({
        where: { order: { customer: { id: userId } } },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DeliverySelect,
      }),
      this.prisma.delivery.count({
        where: { order: { customer: { id: userId } } },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
}
