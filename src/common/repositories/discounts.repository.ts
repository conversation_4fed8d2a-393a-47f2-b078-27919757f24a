import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import {
  IDiscountRepository,
  PaginatedResult,
  PaginationParams,
} from '../interfaces';
import { Discount, DiscountSelect, NewDiscount } from '../schemas';

@Injectable()
export class DiscountRepository implements IDiscountRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewDiscount): Promise<Discount> {
    return this.prisma.discount.create({ data });
  }

  async findOrCreteFistOrderDiscount(): Promise<Discount> {
    let discount = await this.prisma.discount.findUnique({
      where: { code: 'FIRSTORDER' },
    });

    if (!discount) {
      discount = await this.prisma.discount.create({
        data: {
          code: 'FIRSTORDER',
          type: 'PERCENTAGE',
          value: 15,
          isActive: true,
          startDate: new Date('2023-01-01'),
          endDate: new Date('2099-12-31'),
          usageLimit: null,
        },
      });
    }

    return discount;
  }

  async findAll(params: PaginationParams): Promise<PaginatedResult<Discount>> {
    const { page, limit, sortBy, sortOrder } = params;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.discount.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DiscountSelect,
      }),
      this.prisma.discount.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<Discount> {
    return this.prisma.discount.findUnique({ where: { id } });
  }

  async findByCode(code: string): Promise<Discount> {
    return this.prisma.discount.findUnique({ where: { code } });
  }

  async update(id: string, data: any): Promise<Discount> {
    return this.prisma.discount.update({ where: { id }, data });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.discount.delete({ where: { id } });
  }
}
