import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IDriversRepository } from '../interfaces';
import { Driver, DriverSelect, NewDriver } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class DriversRepository implements IDriversRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewDriver): Promise<Driver> {
    return this.prisma.driverDetails.create({ data, select: DriverSelect });
  }

  async findByLinsenceNumber(licenseNumber: string): Promise<Driver> {
    return this.prisma.driverDetails.findUnique({
      where: { licenseNumber },
      select: DriverSelect,
    });
  }

  async findAll(pagination: any): Promise<any> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.driverDetails.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DriverSelect,
      }),
      this.prisma.driverDetails.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
  async findAvailableDrivers(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Driver>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.driverDetails.findMany({
        where: { currentStatus: 'AVAILABLE' },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: DriverSelect,
      }),
      this.prisma.driverDetails.count({
        where: { currentStatus: 'AVAILABLE' },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByUserId(userId: string): Promise<Driver> {
    return this.prisma.driverDetails.findUnique({
      where: { userId },
      select: DriverSelect,
    });
  }

  async findOne(id: string): Promise<Driver> {
    return this.prisma.driverDetails.findUnique({
      where: { id },
      select: DriverSelect,
    });
  }

  async update(id: string, data: any): Promise<Driver> {
    return this.prisma.driverDetails.update({
      data,
      where: { id },
      select: DriverSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.driverDetails.delete({ where: { id } });
  }
}
