import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { INotificationsRepository } from '../interfaces';
import { NewNotification, Notification, NotificationSelect } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class NotificationsRepository implements INotificationsRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewNotification): Promise<Notification> {
    return this.prisma.notification.create({
      data,
      select: NotificationSelect,
    });
  }

  async createManyWithUserId(
    userId: string[],
    notifications: {
      title: string;
      body: string;
      data?: Record<string, any>;
      isRead?: boolean;
      sentAt?: Date;
    },
  ): Promise<Notification[]> {
    return this.prisma.$transaction(
      userId.map((id) =>
        this.prisma.notification.create({
          data: {
            ...notifications,
            user: { connect: { id } },
          },
          select: NotificationSelect,
        }),
      ),
    );
  }

  async count(where: any): Promise<number> {
    return this.prisma.notification.count(where);
  }

  async unreadCount(userId: string): Promise<number> {
    return this.prisma.notification.count({ where: { userId, isRead: false } });
  }
  async findOne(id: string): Promise<Notification> {
    return this.prisma.notification.findUnique({
      where: { id },
      select: NotificationSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.notification.delete({ where: { id } });
  }

  async markAllAsRead(userId: string): Promise<void> {
    await this.prisma.notification.updateMany({
      where: { userId },
      data: { isRead: true },
    });
  }

  async markAsRead(userId: string, notificationId: string): Promise<void> {
    await this.prisma.notification.update({
      where: { id: notificationId, userId },
      data: { isRead: true },
    });
  }

  async deleteAllNotifications(userId: string): Promise<void> {
    await this.prisma.notification.deleteMany({ where: { userId } });
  }

  async findAllByUser(
    userId: string,
    pagination: PaginationParams,
    isRead?: boolean,
  ): Promise<PaginatedResult<Notification>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.notification.findMany({
        where: { userId, isRead },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: NotificationSelect,
      }),
      this.prisma.notification.count({ where: { userId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
}
