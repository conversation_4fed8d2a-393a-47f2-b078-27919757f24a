import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IOrdersRepository } from '../interfaces';
import { NewOrder, Orders, OrderSelect } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';
import { OrderStatus } from '@prisma/client';

@Injectable()
export class OrdersRepository implements IOrdersRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewOrder): Promise<Orders> {
    return this.prisma.order.create({ data, select: OrderSelect });
  }

  async findOrderByStatus(
    userId: string,
    status: OrderStatus,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;
    const [data, total] = await Promise.all([
      this.prisma.order.findMany({
        where: { status, customerId: userId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: OrderSelect,
      }),
      this.prisma.order.count({ where: { status, customerId: userId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async getDailyOrders(date: string): Promise<any> {
    const targetDate = date ? new Date(date) : new Date();

    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const orders = await this.prisma.order.findMany({
      where: {
        createdAt: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            fullname: true,
            email: true,
            phone: true,
          },
        },
        items: {
          include: {
            product: true,
          },
        },
        delivery: {
          include: {
            dropoffAddress: true,
            currentDriver: {
              include: {
                user: {
                  select: {
                    id: true,
                    fullname: true,
                    phone: true,
                  },
                },
              },
            },
            statusHistory: {
              orderBy: {
                createdAt: 'desc',
              },
            },
          },
        },
        payment: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return orders;
  }

  async getOrderStats(
    startDate: Date,
    endDate: Date,
  ): Promise<{
    totalOrders: number;
    ordersByStatus: { status: OrderStatus; _count: { id: number } }[];
    revenue: number;
    avgOrderValue: number;
    ordersPerDay: any;
  }> {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate || new Date();

    const [totalOrders, ordersByStatus, revenue, ordersPerDay] =
      await Promise.all([
        this.prisma.order.count({
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
        this.prisma.order.groupBy({
          by: ['status'],
          _count: {
            id: true,
          },
          where: {
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
        this.prisma.order.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            status: {
              in: [OrderStatus.DELIVERED, OrderStatus.ACCEPTED],
            },
            createdAt: {
              gte: start,
              lte: end,
            },
          },
        }),
        this.prisma.order.aggregateRaw({
          pipeline: [
            {
              $match: {
                createdAt: { $gte: start, $lte: end },
              },
            },
            {
              $group: {
                _id: {
                  $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
                },
                count: { $sum: 1 },
              },
            },
            {
              $project: {
                _id: 0,
                date: '$_id',
                count: 1,
              },
            },
            {
              $sort: { date: 1 },
            },
          ],
        }),
      ]);

    return {
      totalOrders,
      ordersByStatus,
      revenue: revenue._sum.totalAmount || 0,
      avgOrderValue:
        totalOrders > 0 ? (revenue._sum.totalAmount || 0) / totalOrders : 0,
      ordersPerDay,
    };
  }

  async findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.order.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: OrderSelect,
      }),
      this.prisma.order.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByCustomer(
    customerId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.order.findMany({
        where: { customer: { id: customerId } },
        skip,
        take: limit,
        select: OrderSelect,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.order.count({ where: { customerId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByDriver(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Orders>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.order.findMany({
        where: { delivery: { currentDriverId: driverId } },
        skip,
        take: limit,
        select: OrderSelect,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.order.count({
        where: { delivery: { currentDriverId: driverId } },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findWithFilters(
    pagination: PaginationParams,
    startDate?: Date,
    endDate?: Date,
    status?: OrderStatus,
    customerId?: string,
  ): Promise<PaginatedResult<Orders>> {
    const { page, limit, sortOrder, sortBy = 'createdAt' } = pagination;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    if (status) where.status = status;

    if (customerId) where.customerId = customerId;

    const [data, total] = await Promise.all([
      this.prisma.order.findMany({
        where: { ...where },
        skip,
        take: limit,
        select: OrderSelect,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.order.count({
        where: { ...where },
      }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async getUserOrderCount(userId: string): Promise<number> {
    return this.prisma.order.count({ where: { customerId: userId } });
  }

  async findOne(id: string): Promise<Orders> {
    return this.prisma.order.findUnique({ where: { id }, select: OrderSelect });
  }

  async update(id: string, data: Partial<NewOrder>): Promise<Orders> {
    return this.prisma.order.update({
      where: { id },
      data,
      select: OrderSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      await tx.orderItem.deleteMany({ where: { orderId: id } });
      await tx.statusHistory.deleteMany({
        where: { delivery: { orderId: id } },
      });
      await tx.delivery.deleteMany({ where: { orderId: id } });
      await tx.payment.deleteMany({ where: { orderId: id } });
      await tx.order.delete({ where: { id } });
    });
  }
}
