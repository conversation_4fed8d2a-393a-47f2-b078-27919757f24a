import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IOtpRepository } from '../interfaces';
import { Otp, OtpSelect } from '../schemas';

@Injectable()
export class OtpRepository implements IOtpRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Otp> {
    return this.prisma.oTP.create({ data, select: OtpSelect });
  }

  async findOne(token: string): Promise<Otp> {
    return this.prisma.oTP.findUnique({
      where: { code: token },
      select: OtpSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.oTP.delete({ where: { id } });
  }
}
