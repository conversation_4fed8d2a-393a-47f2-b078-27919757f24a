import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IPaymentAccountsRepository } from '../interfaces';
import {
  NewPaymentAccount,
  PaymentAccount,
  PaymentAccountSelect,
} from '../schemas';

@Injectable()
export class PaymentAccountsRepository implements IPaymentAccountsRepository {
  constructor(private readonly prisma: PrismaService) {}
  async create(data: NewPaymentAccount): Promise<PaymentAccount> {
    return this.prisma.whiteListNumbers.create({
      data,
      select: PaymentAccountSelect,
    });
  }

  async findByUserId(userId: string): Promise<PaymentAccount[]> {
    return this.prisma.whiteListNumbers.findMany({
      where: { profile: { userId } },
      select: PaymentAccountSelect,
    });
  }
  async findOne(id: string): Promise<PaymentAccount> {
    return this.prisma.whiteListNumbers.findUnique({
      where: { id },
      select: PaymentAccountSelect,
    });
  }

  async findByNumber(number: string): Promise<PaymentAccount> {
    return this.prisma.whiteListNumbers.findFirst({
      where: { number },
      select: PaymentAccountSelect,
    });
  }

  async findAll(): Promise<PaymentAccount[]> {
    return this.prisma.whiteListNumbers.findMany({
      select: PaymentAccountSelect,
    });
  }

  async update(id: string, data: any): Promise<PaymentAccount> {
    return this.prisma.whiteListNumbers.update({
      where: { id },
      data,
      select: PaymentAccountSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.whiteListNumbers.delete({ where: { id } });
  }
}
