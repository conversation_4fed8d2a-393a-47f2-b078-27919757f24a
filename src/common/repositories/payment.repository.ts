import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import {
  IPaymentRepository,
  PaginatedResult,
  PaginationParams,
} from '../interfaces';
import { Payment, NewPayment, PaymentSelect, UpdatePayment } from '../schemas';
import { PaymentGateways } from '@prisma/client';

@Injectable()
export class PaymentRepository implements IPaymentRepository {
  constructor(private readonly prisma: PrismaService) {}

  getTotalAmount(): Promise<any> {
    return this.prisma.payment.aggregate({
      _sum: {
        amount: true,
      },
      where: {
        status: 'SUCCESSFUL',
      },
    });
  }

  async create(data: NewPayment): Promise<Payment> {
    return this.prisma.payment.create({ data, select: PaymentSelect });
  }

  async getStatistics(startDate: Date, endDate: Date): Promise<any> {
    const [
      totalPayments,
      successfulPayments,
      failedPayments,
      pendingPayments,
      totalAmount,
      paymentsPerDay,
    ] = await Promise.all([
      this.prisma.payment.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      this.prisma.payment.count({
        where: {
          status: 'SUCCESSFUL',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      this.prisma.payment.count({
        where: {
          status: 'FAILED',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      this.prisma.payment.count({
        where: {
          status: 'PENDING',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      }),
      this.prisma.payment.aggregate({
        where: {
          status: 'SUCCESSFUL',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _sum: {
          amount: true,
        },
      }),
      this.prisma.payment.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
        _sum: {
          amount: true,
        },
      }),
    ]);

    return {
      summary: {
        totalPayments,
        successfulPayments,
        failedPayments,
        pendingPayments,
        successRate: totalPayments
          ? (successfulPayments / totalPayments) * 100
          : 0,
        totalAmount: totalAmount._sum.amount || 0,
      },
      dailyStats: paymentsPerDay.map((day) => ({
        date: day.createdAt,
        count: day._count.id,
        amount: day._sum.amount || 0,
      })),
    };
  }

  async updateByPaymentId(
    paymentId: string,
    data: UpdatePayment,
  ): Promise<Payment> {
    return this.prisma.payment.update({
      where: { paymentId },
      data,
      select: PaymentSelect,
    });
  }

  async findByPaymentId(paymentId: string): Promise<Payment> {
    return this.prisma.payment.findUnique({
      where: { paymentId },
      select: PaymentSelect,
    });
  }
  async findByGateway(
    paymentId: string,
    gateway: PaymentGateways,
  ): Promise<Payment> {
    return this.prisma.payment.findUnique({
      where: { paymentId, gateway },
      select: PaymentSelect,
    });
  }

  async findOne(id: string): Promise<Payment> {
    return this.prisma.payment.findUnique({
      where: { id },
      select: PaymentSelect,
    });
  }

  async findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Payment>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.payment.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: PaymentSelect,
      }),
      this.prisma.payment.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByUserId(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Payment>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.payment.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: PaymentSelect,
      }),
      this.prisma.payment.count({ where: { userId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByOrderId(orderId: string): Promise<Payment> {
    return this.prisma.payment.findUnique({
      where: { orderId },
      select: PaymentSelect,
    });
  }

  async update(id: string, data: Partial<NewPayment>): Promise<Payment> {
    return this.prisma.payment.update({
      where: { id },
      data,
      select: PaymentSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.payment.delete({ where: { id } });
  }
}
