import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IProductCategoryRepository } from '../interfaces';
import {
  NewProductCategory,
  ProductCategory,
  ProductCategorySelect,
} from '../schemas';

@Injectable()
export class ProductCategoryRepository implements IProductCategoryRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewProductCategory): Promise<ProductCategory> {
    return this.prisma.productCategory.create({
      data,
      select: ProductCategorySelect,
    });
  }

  async findOne(id: string): Promise<ProductCategory> {
    return this.prisma.productCategory.findUnique({
      where: { id },
      select: ProductCategorySelect,
    });
  }

  async findByName(name: string): Promise<ProductCategory> {
    return this.prisma.productCategory.findUnique({
      where: { name },
      select: ProductCategorySelect,
    });
  }

  async findAll(): Promise<ProductCategory[]> {
    return this.prisma.productCategory.findMany({
      select: ProductCategorySelect,
    });
  }

  async update(
    id: string,
    updateProductCategoryDto: Partial<NewProductCategory>,
  ): Promise<ProductCategory> {
    return this.prisma.productCategory.update({
      where: { id },
      data: updateProductCategoryDto,
      select: ProductCategorySelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.productCategory.delete({ where: { id } });
  }
}
