import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import {
  IProductSourceRepository,
  PaginatedResult,
  PaginationParams,
} from '../interfaces';
import {
  NewProductSource,
  ProductSource,
  ProductSourceSelect,
} from '../schemas';

@Injectable()
export class ProductSourceRepository implements IProductSourceRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findByPlacesId(placeId: string): Promise<ProductSource> {
    return this.prisma.productSource.findUnique({
      where: { placeId },
      select: ProductSourceSelect,
    });
  }

  async create(data: NewProductSource): Promise<ProductSource> {
    return this.prisma.productSource.create({
      data,
      select: ProductSourceSelect,
    });
  }

  async createBulk(data: NewProductSource[]): Promise<any> {
    return this.prisma.productSource.createMany({
      data,
    });
  }

  async findByPlacesIds(placeIds: string[]): Promise<ProductSource[]> {
    return this.prisma.productSource.findMany({
      where: { placeId: { in: placeIds } },
      select: ProductSourceSelect,
    });
  }
  async findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<ProductSource>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.productSource.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ProductSourceSelect,
      }),
      this.prisma.productSource.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async search(data: any): Promise<any> {
    return this.prisma.productSource.findMany({
      where: data,
      select: ProductSourceSelect,
    });
  }

  async findOne(id: string): Promise<ProductSource> {
    return this.prisma.productSource.findUnique({
      where: { id },
      select: ProductSourceSelect,
    });
  }

  async update(
    id: string,
    data: Partial<NewProductSource>,
  ): Promise<ProductSource> {
    return this.prisma.productSource.update({
      data,
      where: { id },
      select: ProductSourceSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.productSource.delete({ where: { id } });
  }

  async bulkDelete(ids: string[]): Promise<void> {
    await this.prisma.productSource.deleteMany({ where: { id: { in: ids } } });
  }
}
