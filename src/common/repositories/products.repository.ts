import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IProductRepository } from '../interfaces';
import { NewProduct, ProductSelect, Products } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class ProductRepository implements IProductRepository {
  constructor(private readonly prisma: PrismaService) {}
  findMany(ids: string[]): Promise<Products[]> {
    return this.prisma.product.findMany({
      where: { id: { in: ids } },
      select: ProductSelect,
    });
  }

  async create(data: NewProduct): Promise<Products> {
    return this.prisma.product.create({
      data,
      select: ProductSelect,
    });
  }

  async createBulk(data: NewProduct[]): Promise<any> {
    return this.prisma.product.createMany({
      data,
    });
  }

  async getBySourceCategory(
    id: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Products>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.product.findMany({
        where: { productSourceId: id },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ProductSelect,
      }),
      this.prisma.product.count({ where: { productSourceId: id } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async getByCategory(
    id: string,
    pagination: PaginationParams,
  ): Promise<Products[]> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    return this.prisma.product.findMany({
      where: { categoryId: id },
      skip,
      take: limit,
      orderBy: {
        [sortBy]: sortOrder,
      },
      select: ProductSelect,
    });
  }

  async getProductByProductSource(
    id: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Products>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.product.findMany({
        where: { productSourceId: id },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ProductSelect,
      }),
      this.prisma.product.count({ where: { productSourceId: id } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Products>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.product.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ProductSelect,
      }),
      this.prisma.product.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
  async search(data: any): Promise<any> {
    return this.prisma.product.findMany({
      where: data,
      select: ProductSelect,
    });
  }

  async findOne(id: string): Promise<Products> {
    return this.prisma.product.findUnique({
      where: { id },
      select: ProductSelect,
    });
  }

  async update(id: string, data: Partial<NewProduct>): Promise<Products> {
    return this.prisma.product.update({
      data,
      where: { id },
      select: ProductSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.product.delete({ where: { id } });
  }
}
