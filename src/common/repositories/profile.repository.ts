import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IProfileRepository } from '../interfaces';
import { NewProfile, Profile, profileSelect } from '../schemas';

@Injectable()
export class ProfileRepository implements IProfileRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Profile> {
    return this.prisma.profile.create({ data, select: profileSelect });
  }

  async findOne(id: string): Promise<Profile> {
    return this.prisma.profile.findUnique({
      where: { userId: id },
      select: profileSelect,
    });
  }

  async update(id: string, data: Partial<NewProfile>): Promise<Profile> {
    return this.prisma.profile.update({
      data,
      where: { id },
      select: profileSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.profile.delete({ where: { id } });
  }
}
