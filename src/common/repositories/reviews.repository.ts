import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IReviewsRepository } from '../interfaces';
import { NewReview, Review, ReviewSelect } from '../schemas';
import { PaginatedResult, PaginationParams } from '../interfaces';

@Injectable()
export class ReviewsRepository implements IReviewsRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewReview): Promise<Review> {
    return this.prisma.review.create({
      data,
      select: ReviewSelect,
    });
  }

  async findOne(id: string): Promise<Review> {
    return this.prisma.review.findUnique({
      where: { id },
      select: ReviewSelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.review.delete({
      where: { id },
    });
  }

  async findByUser(
    userId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Review>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.review.findMany({
        where: { reviewer: { id: userId } },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ReviewSelect,
      }),
      this.prisma.review.count({ where: { reviewerId: userId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findByDriver(
    driverId: string,
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Review>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.review.findMany({
        where: { driverDetails: { id: driverId } },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ReviewSelect,
      }),
      this.prisma.review.count({ where: { driverDetailsId: driverId } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAll(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Review>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.review.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: ReviewSelect,
      }),
      this.prisma.review.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
}
