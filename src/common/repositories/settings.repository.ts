import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ISettingsRepository } from '../interfaces';
import { Setting } from '../schemas';

@Injectable()
export class SettingsRepository implements ISettingsRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Setting> {
    return this.prisma.setting.create({ data });
  }

  async findAll(): Promise<Setting[]> {
    return this.prisma.setting.findMany();
  }

  async findOne(key: string): Promise<Setting> {
    return this.prisma.setting.findUnique({ where: { key } });
  }

  async update(key: string, data: any): Promise<Setting> {
    return this.prisma.setting.update({ where: { key }, data });
  }

  async remove(key: string): Promise<void> {
    await this.prisma.setting.delete({ where: { key } });
  }
}
