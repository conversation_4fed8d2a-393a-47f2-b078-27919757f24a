import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { ISourceCategoryRepository } from '../interfaces';
import {
  NewSourceCategory,
  SourceCategory,
  SourceCategorySelect,
} from '../schemas';

@Injectable()
export class SourceCategoryRepository implements ISourceCategoryRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewSourceCategory): Promise<SourceCategory> {
    return this.prisma.productSourceCategory.create({
      data,
      select: SourceCategorySelect,
    });
  }

  async findOne(id: string): Promise<SourceCategory> {
    return this.prisma.productSourceCategory.findUnique({
      where: { id },
      select: SourceCategorySelect,
    });
  }

  async findByName(name: string): Promise<SourceCategory> {
    return this.prisma.productSourceCategory.findUnique({
      where: { name },
      select: SourceCategorySelect,
    });
  }

  async findAll(): Promise<SourceCategory[]> {
    return this.prisma.productSourceCategory.findMany({
      select: SourceCategorySelect,
    });
  }

  async update(
    id: string,
    data: Partial<NewSourceCategory>,
  ): Promise<SourceCategory> {
    return this.prisma.productSourceCategory.update({
      data,
      where: { id },
      select: SourceCategorySelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.productSourceCategory.delete({ where: { id } });
  }
}
