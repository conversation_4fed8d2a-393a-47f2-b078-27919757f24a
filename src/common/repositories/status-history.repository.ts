import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { IStatusHistoryRepository } from '../interfaces';
import {
  NewStatusHistory,
  StatusHistory,
  StatusHistorySelect,
} from '../schemas';

@Injectable()
export class StatusHistoryRepository implements IStatusHistoryRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: NewStatusHistory): Promise<StatusHistory> {
    return this.prisma.statusHistory.create({
      data,
      select: StatusHistorySelect,
    });
  }

  async findAllByDeliveryId(id: string): Promise<StatusHistory[]> {
    return this.prisma.statusHistory.findMany({
      where: { deliveryId: id },
      select: StatusHistorySelect,
    });
  }

  async findOne(id: string): Promise<StatusHistory> {
    return this.prisma.statusHistory.findUnique({
      where: { id },
      select: StatusHistorySelect,
    });
  }

  async update(id: string, data: any): Promise<StatusHistory> {
    return this.prisma.statusHistory.update({
      where: { id },
      data,
      select: StatusHistorySelect,
    });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.statusHistory.delete({ where: { id } });
  }
}
