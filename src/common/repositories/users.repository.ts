import { PrismaService } from 'src/core/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import { PaginatedResult, IUsersRepository } from '../interfaces';
import { PaginationParams } from '../interfaces';
import { Users, UserSelect } from '../schemas';
import { User } from '@prisma/client';

@Injectable()
export class UsersRepository implements IUsersRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Users> {
    return this.prisma.user.create({ data, select: UserSelect });
  }

  async getAdmins(): Promise<Users[]> {
    return this.prisma.user.findMany({
      where: { role: 'ADMIN' },
      select: UserSelect,
    });
  }

  async findManyUsersById(ids: string[]): Promise<Users[]> {
    return this.prisma.user.findMany({
      where: { id: { in: ids } },
      select: UserSelect,
    });
  }
  async getUserWithPassword(identifier: string): Promise<User> {
    return this.prisma.user.findFirst({
      where: {
        OR: [{ id: identifier }, { email: identifier }],
      },
    });
  }

  async getTrashedAccounts(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        where: { isDeleted: true },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: UserSelect,
      }),
      this.prisma.user.count({ where: { isDeleted: true } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAllDrivers(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        where: { role: 'DRIVER' },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: UserSelect,
      }),
      this.prisma.user.count({ where: { role: 'DRIVER' } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }
  async findAllCustomers(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        where: { role: 'CUSTOMER' },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: UserSelect,
      }),
      this.prisma.user.count({ where: { role: 'CUSTOMER' } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAllAdmins(
    pagination: PaginationParams,
  ): Promise<PaginatedResult<Users>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        where: { role: 'ADMIN' },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: UserSelect,
      }),
      this.prisma.user.count({ where: { role: 'ADMIN' } }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findAll(pagination: PaginationParams): Promise<PaginatedResult<Users>> {
    const { page, limit, sortBy, sortOrder } = pagination;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.prisma.user.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        select: UserSelect,
      }),
      this.prisma.user.count(),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<Users> {
    return this.prisma.user.findUnique({
      where: { id },
      select: UserSelect,
    });
  }

  async getUserByEmail(email: string): Promise<Users> {
    return this.prisma.user.findUnique({
      where: { email },
      select: UserSelect,
    });
  }
  async getUserByIdentifier(identifier: string): Promise<User> {
    let user = await this.prisma.user.findUnique({
      where: { email: identifier },
    });

    if (!user)
      user = await this.prisma.user.findFirst({
        where: { phone: identifier },
      });

    return user;
  }

  async getUserByPhone(phone: string): Promise<Users> {
    return this.prisma.user.findFirst({
      where: { phone },
      select: UserSelect,
    });
  }

  async addGoogleUser(data: any): Promise<Users> {
    return this.prisma.user.create({ data, select: UserSelect });
  }

  async updateFirebaseToken(id: string, data: Partial<User>): Promise<Users> {
    return this.prisma.user.update({ where: { id }, data, select: UserSelect });
  }

  async updateExpoToken(id: string, data: any): Promise<Users> {
    return this.prisma.user.update({ where: { id }, data, select: UserSelect });
  }

  async softDelete(id: string): Promise<void> {
    await this.prisma.user.update({ where: { id }, data: { isDeleted: true } });
  }

  async restore(id: string): Promise<Users> {
    return this.prisma.user.update({
      where: { id },
      select: UserSelect,
      data: { isDeleted: false, deletedAt: null },
    });
  }

  async update(id: string, data: any): Promise<Users> {
    return this.prisma.user.update({ where: { id }, data, select: UserSelect });
  }

  async remove(id: string): Promise<void> {
    await this.prisma.$transaction([
      this.prisma.oTP.deleteMany({ where: { userId: id } }),
      this.prisma.profile.deleteMany({ where: { userId: id } }),
      this.prisma.address.deleteMany({ where: { userId: id } }),
      this.prisma.notification.deleteMany({ where: { userId: id } }),
      this.prisma.payment.deleteMany({ where: { userId: id } }),
      this.prisma.chat.deleteMany({ where: { userId: id } }),
      this.prisma.message.deleteMany({ where: { senderId: id } }),
      this.prisma.driverDetails.deleteMany({ where: { userId: id } }),
      this.prisma.adminDetails.deleteMany({ where: { userId: id } }),
      this.prisma.user.delete({ where: { id } }),
    ]);
  }
}
