import { Prisma } from '@prisma/client';

const addressSelect = {
  id: true,
  street: true,
  city: true,
  state: true,
  country: true,
  latitude: true,
  longitude: true,
  label: true,
  isDefault: true,
  createdAt: true,
  updatedAt: true,
} as const;

export const AddressSelect = addressSelect;
export type NewAddress = Prisma.AddressCreateInput;
export type Address = Prisma.AddressGetPayload<{
  select: typeof addressSelect;
}>;
