import { Prisma } from '@prisma/client';

const adminDetailsSelect = {
  id: true,
  permissions: true,
  createdAt: true,
  updatedAt: true,
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
      isVerified: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      role: true,
    },
  },
} as const;

export const AdminDetailsSelect = adminDetailsSelect;
export type NewAdminDetails = Prisma.AdminDetailsCreateInput;
export type AdminDetails = Prisma.AdminDetailsGetPayload<{
  select: typeof adminDetailsSelect;
}>;
