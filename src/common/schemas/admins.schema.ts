import { Prisma } from '@prisma/client';

const adminSelect = {
  id: true,
  email: true,
  fullname: true,
  phone: true,
  role: true,
  isVerified: true,
  createdAt: true,
  updatedAt: true,
  adminDetails: {
    select: {
      permissions: true,
    },
  },
} as const;

export const AdminSelect = adminSelect;
export type NewAdmin = Prisma.AdminDetailsCreateInput;
export type Admin = Prisma.UserGetPayload<{
  select: typeof adminSelect;
}>;
