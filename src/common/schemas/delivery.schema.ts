import { Prisma } from '@prisma/client';

const deliverySelect = {
  id: true,
  status: true,
  fee: true,
  updatedAt: true,
  estimatedDistance: true,
  pickupAddresses: true,
  estimatedDuration: true,
  actualDistance: true,
  actualDuration: true,
  startTime: true,
  endTime: true,
  currentLong: true,
  currentLat: true,
  dropoffAddress: {
    select: {
      id: true,
      label: true,
      street: true,
      city: true,
      state: true,
      country: true,
      latitude: true,
      longitude: true,
    },
  },
  currentDriver: {
    select: {
      id: true,
      user: {
        select: {
          id: true,
          fullname: true,
          phone: true,
          email: true,
          profile: {
            select: {
              avatar: true,
              nickname: true,
              gender: true,
            },
          },
        },
      },
    },
  },
  statusHistory: {
    select: {
      status: true,
      notes: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  },
  order: {
    select: {
      id: true,
      status: true,
      totalAmount: true,
      customer: {
        select: {
          id: true,
          fullname: true,
          email: true,
          phone: true,
        },
      },
      items: {
        select: {
          id: true,
          quantity: true,
          price: true,
          product: {
            select: {
              name: true,
              productSource: {
                select: {
                  id: true,
                  name: true,
                  long: true,
                  lat: true,
                },
              },
            },
          },
        },
      },
    },
  },
} as const;

export const DeliverySelect = deliverySelect;
export type NewDelivery = Prisma.DeliveryCreateInput;
export type Delivery = Prisma.DeliveryGetPayload<{
  select: typeof deliverySelect;
}>;

export type UpdateDelivery = Prisma.DeliveryUpdateInput;
