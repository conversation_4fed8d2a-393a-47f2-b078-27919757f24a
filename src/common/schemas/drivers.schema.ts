import { Prisma } from '@prisma/client';

const driverSelect = {
  id: true,
  licenseNumber: true,
  vehicleType: true,
  vehicleModel: true,
  isAvailable: true,
  currentStatus: true,
  createdAt: true,
  updatedAt: true,
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
      isVerified: true,
      role: true,
    },
  },
} as const;

export const DriverSelect = driverSelect;
export type NewDriver = Prisma.DriverDetailsCreateInput;
export type Driver = Prisma.DriverDetailsGetPayload<{
  select: typeof driverSelect;
}>;
