import { Prisma } from '@prisma/client';

const notificationSelect = {
  id: true,
  title: true,
  body: true,
  data: true,
  isRead: true,
  createdAt: true,
  sentAt: true,
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
      role: true,
      expoPushToken: true,
      fcmToken: true,
    },
  },
} as const;

export const NotificationSelect = notificationSelect;
export type NewNotification = Prisma.NotificationCreateInput;
export type Notification = Prisma.NotificationGetPayload<{
  select: typeof notificationSelect;
}>;
