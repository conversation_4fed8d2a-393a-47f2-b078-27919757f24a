import { Prisma } from '@prisma/client';

const orderSelect = {
  id: true,
  status: true,
  totalAmount: true,
  createdAt: true,
  orderPreference: true,
  updatedAt: true,
  isPayed: true,
  discount: true,
  discountAmount: true,
  customer: {
    select: {
      id: true,
      fullname: true,
      email: true,
      phone: true,
    },
  },
  payment: true,
  items: {
    select: {
      id: true,
      quantity: true,
      price: true,
      product: {
        select: {
          name: true,
          price: true,
          productSource: {
            select: {
              name: true,
              long: true,
              lat: true,
            },
          },
        },
      },
    },
  },
  delivery: {
    select: {
      id: true,
      status: true,
      fee: true,
      estimatedDistance: true,
      estimatedDuration: true,
      currentLong: true,
      currentLat: true,
      pickupAddresses: true,
      dropoffAddress: {
        select: {
          id: true,
          city: true,
          country: true,
          label: true,
          longitude: true,
          latitude: true,
        },
      },
      currentDriver: {
        select: {
          id: true,
          user: {
            select: {
              id: true,
              fullname: true,
              phone: true,
              email: true,
              driverDetails: {
                select: {
                  licenseNumber: true,
                  vehicleType: true,
                  vehicleModel: true,
                },
              },
            },
          },
        },
      },
      statusHistory: {
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          status: true,
          notes: true,
          createdAt: true,
        },
      },
    },
  },
} as const;

export const OrderSelect = orderSelect;
export type NewOrder = Prisma.OrderCreateInput;
export type Orders = Prisma.OrderGetPayload<{
  select: typeof orderSelect;
}>;
