import { Prisma } from '@prisma/client';

const otpSelect = {
  id: true,
  code: true,
  type: true,
  expiresAt: true,
  isUsed: true,
  createdAt: true,
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
    },
  },
} as const;

export const OtpSelect = otpSelect;
export type NewOtp = Prisma.OTPCreateInput;
export type Otp = Prisma.OTPGetPayload<{
  select: typeof otpSelect;
}>;
