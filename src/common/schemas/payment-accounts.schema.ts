import { Prisma } from '@prisma/client';

const paymentAccountSelect = {
  id: true,
  number: true,
  createdAt: true,
  isVerified: true,
  updatedAt: true,
  profile: {
    select: {
      id: true,
      nickname: true,
      gender: true,
      dateOfBirth: true,
      otherNumbers: true,
      user: {
        select: {
          id: true,
          email: true,
          fullname: true,
          phone: true,
          role: true,
        },
      },
    },
  },
} as const;

export const PaymentAccountSelect = paymentAccountSelect;
export type NewPaymentAccount = Prisma.WhiteListNumbersCreateInput;
export type PaymentAccount = Prisma.WhiteListNumbersGetPayload<{
  select: typeof paymentAccountSelect;
}>;
