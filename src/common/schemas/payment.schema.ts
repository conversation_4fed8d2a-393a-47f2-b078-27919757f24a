import { Prisma } from '@prisma/client';

const paymentSelect = {
  id: true,
  paymentId: true,
  amount: true,
  currency: true,
  gateway: true,
  phoneNumber: true,
  description: true,
  status: true,
  channel: true,
  transactionUUID: true,
  paymentUrl: true,
  ref: true,
  mobileOperator: true,
  userEmail: true,
  refundAmount: true,
  refundReason: true,
  retryCount: true,
  failureReason: true,
  createdAt: true,
  lastRetryAt: true,
  userId: true,
  orderId: true,
  order: {
    select: {
      id: true,
      status: true,
      totalAmount: true,
    },
  },
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
    },
  },
} as const;

export const PaymentSelect = paymentSelect;
export type NewPayment = Prisma.PaymentCreateInput;
export type Payment = Prisma.PaymentGetPayload<{
  select: typeof paymentSelect;
}>;

export type UpdatePayment = Prisma.PaymentUpdateInput;
