import { Prisma } from '@prisma/client';

const productCategorySelect = {
  id: true,
  name: true,
  createdAt: true,
  updatedAt: true,
  products: {
    select: {
      id: true,
      name: true,
      price: true,
      images: true,
    },
  },
} as const;

export const ProductCategorySelect = productCategorySelect;
export type NewProductCategory = Prisma.ProductCategoryCreateInput;
export type ProductCategory = Prisma.ProductCategoryGetPayload<{
  select: typeof productCategorySelect;
}>;
