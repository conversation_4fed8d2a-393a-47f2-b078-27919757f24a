import { Prisma } from '@prisma/client';

const productSourceSelect = {
  id: true,
  name: true,
  rating: true,
  placeId: true,
  description: true,
  totalRating: true,
  createdAt: true,
  updatedAt: true,
  images: true,
  lat: true,
  long: true,
  street: true,
  city: true,
  state: true,
  country: true,
  radius: true,
  products: true,
} as const;

export const ProductSourceSelect = productSourceSelect;
export type NewProductSource = Prisma.ProductSourceCreateInput;
export type ProductSource = Prisma.ProductSourceGetPayload<{
  select: typeof productSourceSelect;
}>;
