import { Prisma } from '@prisma/client';

const productSelect = {
  id: true,
  name: true,
  description: true,
  price: true,
  deliveryPrice: true,
  createdAt: true,
  updatedAt: true,
  images: true,
  // category: {
  //   select: {
  //     id: true,
  //     name: true,
  //     logo: true,
  //   },
  // },
  productSource: {
    select: {
      id: true,
      name: true,
      long: true,
      lat: true,
      placeId: true,
      rating: true,
      images: true,
    },
  },
} as const;

export const ProductSelect = productSelect;
export type NewProduct = Prisma.ProductCreateInput;
export type Products = Prisma.ProductGetPayload<{
  select: typeof productSelect;
}>;
