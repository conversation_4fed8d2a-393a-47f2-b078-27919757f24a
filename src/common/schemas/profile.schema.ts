import { Prisma } from '@prisma/client';

export const profileSelect = {
  id: true,
  nickname: true,
  gender: true,
  avatar: true,
  dateOfBirth: true,
  otherNumbers: true,
  user: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
      isPhoneVerified: true,
      expoPushToken: true,
      fcmToken: true,
      isEmailVerified: true,
      createdAt: true,
      updatedAt: true,
      isVerified: true,
      role: true,
    },
  },
} as const;

export type NewProfile = Prisma.ProfileCreateInput;
export type Profile = Prisma.ProfileGetPayload<{
  select: typeof profileSelect;
}>;
