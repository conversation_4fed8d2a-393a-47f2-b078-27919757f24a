import { Prisma } from '@prisma/client';

const reviewSelect = {
  id: true,
  rating: true,
  comment: true,
  createdAt: true,
  updatedAt: true,
  reviewer: {
    select: {
      id: true,
      email: true,
      fullname: true,
      phone: true,
    },
  },
} as const;

export const ReviewSelect = reviewSelect;
export type NewReview = Prisma.ReviewCreateInput;
export type Review = Prisma.ReviewGetPayload<{
  select: typeof reviewSelect;
}>;
