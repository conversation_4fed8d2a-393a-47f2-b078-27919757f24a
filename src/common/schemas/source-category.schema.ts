import { Prisma } from '@prisma/client';

const sourceCategorySelect = {
  id: true,
  name: true,
  logo: true,
  sources: {
    select: {
      id: true,
      name: true,
      long: true,
      lat: true,
      placeId: true,
      rating: true,
      images: true,
    },
  },
} as const;

export const SourceCategorySelect = sourceCategorySelect;
export type NewSourceCategory = Prisma.ProductSourceCategoryCreateInput;
export type SourceCategory = Prisma.ProductSourceCategoryGetPayload<{
  select: typeof sourceCategorySelect;
}>;
