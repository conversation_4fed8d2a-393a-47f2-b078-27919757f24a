import { Prisma } from '@prisma/client';

const statusHistorySelect = {
  id: true,
  status: true,
  notes: true,
  createdAt: true,
  deliveryId: true,
  delivery: {
    select: {
      id: true,
      status: true,
    },
  },
} as const;

export const StatusHistorySelect = statusHistorySelect;
export type NewStatusHistory = Prisma.StatusHistoryCreateInput;
export type StatusHistory = Prisma.StatusHistoryGetPayload<{
  select: typeof statusHistorySelect;
}>;
