import { Prisma } from '@prisma/client';

const userSelect = {
  id: true,
  email: true,
  fullname: true,
  googleId: true,
  role: true,
  phone: true,
  isVerified: true,
  createdAt: true,
  updatedAt: true,
  address: true,
  profile: true,
  isDeleted: true,
  orders: true,
  deletedAt: true,
  isEmailVerified: true,
  expoPushToken: true,
  fcmToken: true,
  isPhoneVerified: true,
  driverDetails: true,
  adminDetails: true,
} as const;

export const UserSelect = userSelect;
export type NewUser = Prisma.UserCreateInput;
export type Users = Prisma.UserGetPayload<{
  select: typeof userSelect;
}>;
