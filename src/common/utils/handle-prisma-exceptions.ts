import {
  ConflictException,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';

export function handlePrismaError(error: unknown) {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        throw new ConflictException('Unique constraint failed');
      case 'P2003':
        throw new BadRequestException('Foreign key constraint failed');
      case 'P2025':
        throw new NotFoundException('Record not found');
      default:
        throw new Error(error.message);
    }
  } else {
    throw new InternalServerErrorException(error);
  }
}
