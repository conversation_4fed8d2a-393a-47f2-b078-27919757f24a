import { PaginationDto } from '../dto/pagination.dto';
import { PaginatedResult } from '../interfaces';

export function getPaginationMetadata(
  total: number,
  { page, limit }: PaginationDto,
): PaginatedResult<any>['meta'] {
  const totalPages = Math.ceil(total / limit);

  return {
    total,
    page,
    limit,
    totalPages,
    hasNextPage: page < totalPages,
    hasPrevPage: page > 1,
  };
}

export function getPaginationSkip(page: number, limit: number): number {
  return (page - 1) * limit;
}

/**
 * Extracts unique product source coordinates from order items
 * @param items Array of order items with product and productSource information
 * @returns Array of unique product source coordinates with metadata
 */
export function extractProductSourceCoordinates(items: any[]): Array<{
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  placeId?: string;
}> {
  const sourceMap = new Map();

  for (const item of items) {
    if (!item.product?.productSource) continue;

    const source = item.product.productSource;
    const sourceId = source.id || item.product.productSourceId;

    if (sourceId && source.lat && source.long && !sourceMap.has(sourceId)) {
      sourceMap.set(sourceId, {
        id: sourceId,
        name: source.name || 'Unknown Location',
        latitude: Number(source.lat),
        longitude: Number(source.long),
        placeId: source.placeId,
      });
    }
  }

  return Array.from(sourceMap.values());
}
