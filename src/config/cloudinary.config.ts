import { v2 as cloudinary } from 'cloudinary';
import { TransformationOptions } from 'cloudinary';

export const defaultTransformation: TransformationOptions = {
  width: 800,
  height: 600,
  crop: 'limit',
  quality: 'auto:good',
  fetch_format: 'auto',
};

export function configureCloudinary() {
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });
}
