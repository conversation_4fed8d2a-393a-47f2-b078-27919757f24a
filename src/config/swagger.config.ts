import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export const swaggerConfig = (app: INestApplication) => {
  const config = new DocumentBuilder()
    .setTitle('Zakas Backend API')
    .setDescription('')
    .setVersion('1.0')
    .addServer('http://localhost:8888/', 'Local environment')
    .addServer('https://backend-production-cb4f.up.railway.app/')
    .addServer('https://backend-1zck.onrender.com/', 'Production environment')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    })
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/api/zakaz/docs', app, document);
};
