import { Global, Module } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { CacheService } from './cache.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import redisStore from 'cache-manager-ioredis';

@Global()
@Module({
  imports: [
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const redisHost = configService.get('REDIS_HOST');
        const redisPort = configService.get('REDIS_PORT');

        const cacheConfig = {
          ttl: configService.get('CACHE_TTL', 300) * 1000,
          max: configService.get('CACHE_MAX_ITEMS', 10000),
          isGlobal: true,
        };

        if (redisHost && redisPort) {
          try {
            return {
              ...cacheConfig,
              store: redisStore,
              host: redisHost,
              port: redisPort,
              password: configService.get('REDIS_PASSWORD'),
              db: configService.get('REDIS_DB', 0),
            };
          } catch (error) {
            console.warn(
              'Failed to connect to Redis, falling back to in-memory cache',
              error,
            );
            return cacheConfig;
          }
        }
        return cacheConfig;
      },
    }),
  ],
  providers: [CacheService],
  exports: [CacheService],
})
export class AppCacheModule {}
