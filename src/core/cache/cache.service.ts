import { Injectable, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly isRedisAvailable: boolean;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {
    this.isRedisAvailable = !!this.configService.get('REDIS_HOST');
    this.logger.log(
      `Cache service initialized. Using Redis: ${this.isRedisAvailable}`,
    );
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      return await this.cacheManager.get<T>(key);
    } catch (error) {
      this.logger.error(`Error getting cache key ${key}: ${error.message}`);
      return null;
    }
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
    } catch (error) {
      this.logger.error(`Error setting cache key ${key}: ${error.message}`);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
    } catch (error) {
      this.logger.error(`Error deleting cache key ${key}: ${error.message}`);
    }
  }

  async getOrSet<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl?: number,
  ): Promise<T | null> {
    try {
      let data = await this.get<T>(key);
      if (!data) {
        data = await fetchFn();
        if (data) {
          await this.set(key, data, ttl);
        }
      }
      return data;
    } catch (error) {
      this.logger.error(
        `Error in getOrSet for key ${key}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  isUsingRedis(): boolean {
    return this.isRedisAvailable;
  }
}
