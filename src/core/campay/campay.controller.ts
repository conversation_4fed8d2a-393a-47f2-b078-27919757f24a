import {
  Body,
  Controller,
  Get,
  Param,
  UseGuards,
  Post,
  Logger,
} from '@nestjs/common';
import { CampayService } from './campay.service';
import { CreateCampayDto, RetryPaymentDto } from './dto/create.dto';
import { ApiOperation, ApiBody, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { User } from 'src/common/decorators/user.decorator';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { IsPhoneVerifiedGuard } from 'src/common/guards/isPhoneVerified.guard';

@ApiTags('Campay')
@Controller('campay')
export class CampayController {
  private logger = new Logger(CampayController.name);
  constructor(private readonly campayService: CampayService) {}

  @Post('webhook')
  async handleWebhook(@Body() body: any) {
    const {
      status,
      reference,
      amount,
      currency,
      operator,
      code,
      operator_reference,
      signature,
      endpoint,
      external_reference,
      external_user,
      extra_first_name,
      extra_last_name,
      extra_email,
      phone_number,
    } = body;

    this.logger.log(`Received callback for transaction: ${reference}`);

    const isValidSignature =
      await this.campayService.validateSignature(signature);
    if (!isValidSignature) {
      this.logger.warn(`Invalid signature for transaction: ${reference}`);
      return { success: false, message: 'Invalid signature' };
    }

    const transactionData = {
      status,
      reference,
      amount,
      currency,
      operator,
      code,
      operatorReference: operator_reference,
      endpoint,
      externalReference: external_reference,
      externalUser: external_user,
      extraFirstName: extra_first_name,
      extraLastName: extra_last_name,
      extraEmail: extra_email,
      phoneNumber: phone_number,
    };

    const result = await this.campayService.processTransaction(transactionData);
    return result;
  }

  @Post('initiate')
  @ApiOperation({ summary: 'Request a payment' })
  @ApiBody({ type: CreateCampayDto })
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  @ApiBearerAuth()
  async requestPayment(
    @Body() body: CreateCampayDto,
    @User() user,
  ): Promise<any> {
    return this.campayService.requestPayment(body, user.userId);
  }

  // @Post('initiate/withdraw')
  // @ApiOperation({ summary: 'Request a withdrawal' })
  // @ApiBody({ type: CreateCampayDto })
  // @UseGuards(JwtAuthGuard, IsAdminGuard)
  // async requestWithdrawal(
  //   @Body() body: CreateCampayDto,
  //   @User() user,
  // ): Promise<any> {
  //   return this.campayService.initiatePayment(body, user.userId);
  // }

  @Post('/retry')
  @ApiOperation({ summary: 'Retry a payment' })
  @ApiBody({ type: RetryPaymentDto })
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  @ApiBearerAuth()
  retryPayment(@Body() body: RetryPaymentDto) {
    return this.campayService.retryPayment(body.orderId);
  }

  @Get(':paymentId/status')
  @ApiOperation({ summary: 'Get transaction status' })
  async getTransactionStatus(@Param() paymentId: string) {
    return this.campayService.getTransactionStatus(paymentId);
  }

  // @Get(':paymentId/refund')
  // @ApiOperation({ summary: 'Refund funds' })
  // async refundFunds(@Param() paymentId: string) {
  //   return this.campayService.refundFunds();
  // }
  @Get('stats')
  @ApiOperation({ summary: 'Get payment statistics' })
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  async getPaymentStats() {
    return this.campayService.getPaymentStats();
  }

  // @Post('test-payment')
  // async testPayment() {
  //   await this.campayService.collectPayment({
  //     amount: '100',
  //     from: '237658610146',
  //     description: 'Test payment',
  //     external_reference: 'TEST_PAYMENT',
  //     email: '<EMAIL>',
  //     order: null,
  //   });
  //   return { message: 'Payment initiated' };
  // }

  @Get('total-amount')
  @ApiOperation({ summary: 'Get total payment amount' })
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  async getTotalAmount() {
    return this.campayService.getTotalAmount();
  }
}
