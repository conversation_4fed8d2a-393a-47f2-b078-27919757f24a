import { Global, Module } from '@nestjs/common';
import { CampayController } from './campay.controller';
import { CampayService } from './campay.service';
import { NotificationService } from 'src/modules/notification/notification.service';
// import { NotificationModule } from 'src/modules/notification/notification.module';
import { EmailService } from '../email/email.service';
import {
  DeliveryRepository,
  DriversRepository,
  NotificationsRepository,
  OrdersRepository,
  PaymentAccountsRepository,
  PaymentRepository,
  UsersRepository,
} from 'src/common/repositories';
import { NotificationModule } from 'src/modules/notification/notification.module';

@Global()
@Module({
  imports: [NotificationModule],
  providers: [
    NotificationService,
    CampayService,
    PaymentRepository,
    OrdersRepository,
    UsersRepository,
    DeliveryRepository,
    PaymentAccountsRepository,
    DriversRepository,
    EmailService,
    NotificationsRepository,
  ],
  controllers: [CampayController],
  exports: [CampayService],
})
export class CampayModule {}
