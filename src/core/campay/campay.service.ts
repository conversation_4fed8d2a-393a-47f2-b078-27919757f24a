import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { campayHttp } from 'src/config/campay.config';
import { GetTransactionResponse, TransactionData } from './types';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { verify } from 'jsonwebtoken';
import { CreateCampayDto } from './dto/create.dto';
import { NotificationService } from 'src/modules/notification/notification.service';
import { AxiosError } from 'axios';
import { EmailService } from '../email/email.service';
import {
  DeliveryRepository,
  DriversRepository,
  OrdersRepository,
  PaymentAccountsRepository,
  PaymentRepository,
  UsersRepository,
} from 'src/common/repositories';
import { Delivery, Orders, Payment } from 'src/common/schemas';

@Injectable()
export class CampayService {
  private readonly baseUrl: string;
  private readonly pamenantToken: string;
  private readonly serviceSecret: string;

  private logger = new Logger(CampayService.name);

  constructor(
    private configService: ConfigService,
    private readonly notificationService: NotificationService,
    private readonly paymentRepository: PaymentRepository,
    private readonly ordersRepository: OrdersRepository,
    private readonly usersRepository: UsersRepository,
    private readonly deliveryRepository: DeliveryRepository,
    private readonly paymentAccountRepository: PaymentAccountsRepository,
    private readonly driversRepository: DriversRepository,
    private readonly emailService: EmailService,
  ) {
    this.baseUrl = this.configService.get<string>('CAMPAY_BASE_URL');
    this.pamenantToken = this.configService.get<string>(
      'CAMPAY_PAMENENT_ACCESS',
    );
    this.serviceSecret = this.configService.get<string>(
      'CAMPAY_WEBHOOK_SECRET',
    );
  }
  async validatePaymentInfo(userId: string, phone: string) {
    const user = await this.usersRepository.findOne(userId);

    if (phone.trim() !== user.phone.trim()) {
      const whitelistedNumber =
        await this.paymentAccountRepository.findByNumber(phone);

      if (!whitelistedNumber) {
        throw new BadRequestException('Phone number not whitelisted');
      }

      if (whitelistedNumber && !whitelistedNumber.isVerified) {
        throw new BadRequestException('Whitelisted phone number not verified');
      }
    }
  }
  async requestPayment(body: CreateCampayDto, userId?: string) {
    const { amount, from, description, external_reference, email, order } =
      body;

    try {
      const parsedAmount = parseFloat(amount);
      if (isNaN(parsedAmount) || parsedAmount <= 0) {
        throw new BadRequestException('Invalid payment amount');
      }

      let payment = await this.paymentRepository.create({
        paymentId: this.generateUniquePaymentId(),
        amount: parsedAmount,
        currency: 'XAF',
        gateway: 'CAMPAY',
        phoneNumber: from,
        ref: external_reference,
        description,
        status: 'INITIATED',
        userEmail: email,
        retryCount: 0,
        lastRetryAt: null,
        failureReason: null,

        order: {
          connect: { id: order.id },
        },
        user: {
          connect: { id: userId },
        },
      });

      try {
        const res = await campayHttp.post('/collect/', {
          amount: Math.round(parsedAmount),
          from,
          description,
          external_reference,
          extra_email: email,
        });

        if (res.status === 200 && res.data?.reference) {
          this.logger.debug(
            `Payment initiated for order: ${order.id} with reference: ${res.data.reference}`,
          );
          payment = await this.paymentRepository.update(payment.id, {
            paymentId: res.data.reference,
            transactionUUID: res.data.reference,
            status: 'PENDING',
            lastRetryAt: new Date(),
          });
          await this.notificationService.sendSingleNotification(
            userId,
            'Payment Initiated',
            `Payment of ${order.totalAmount} XAF has been initiated. Please wait for the payment to be processed.`,
            {
              type: 'PAYMENT_INITIATED',
              amount: order.totalAmount.toString(),
              paymentId: res.data.reference,
            },
          );
        } else {
          this.logger.error('Invalid response from Campay API', res.data);
          await this.paymentRepository.update(payment.id, {
            status: 'FAILED',
            failureReason: 'Invalid response from payment gateway',
            lastRetryAt: new Date(),
          });
          throw new BadRequestException(
            'Payment gateway returned invalid response',
          );
        }
      } catch (httpError) {
        this.logger.error('Campay API request failed', httpError);
        let errorMessage = 'Payment service temporarily unavailable';

        if (httpError instanceof AxiosError) {
          errorMessage =
            httpError.response?.data?.message ||
            httpError.response?.data?.error ||
            'Payment gateway error';
          throw new BadRequestException(errorMessage);
        }
        await this.paymentRepository.update(payment.id, {
          status: 'FAILED',
          failureReason: errorMessage,
          lastRetryAt: new Date(),
          retryCount: payment.retryCount + 1,
        });

        // Free up the driver if one was assigned
        const order = await this.ordersRepository.findOne(payment.order.id);

        if (order?.delivery?.currentDriver.id) {
          await this.driversRepository.update(order.delivery.currentDriver.id, {
            currentStatus: 'AVAILABLE',
            isAvailable: true,
            updatedAt: new Date(),
          });

          await this.deliveryRepository.update(order.delivery.id, {
            currentDriver: { disconnect: true },
            status: 'PENDING_PAYMENT',
            updatedAt: new Date(),
          });
        }

        if (httpError instanceof AxiosError) {
          throw new BadRequestException(errorMessage);
        }

        // notify user
        await this.notificationService.sendSingleNotification(
          userId,
          'Payment Failed',
          `Payment of ${order.totalAmount} XAF has failed. Please try again`,
          {
            type: 'PAYMENT_FAILED',
            paymentId: payment.paymentId,
          },
        );

        // email admins
        await this.sendFailedPaymentEmailToAdmins(
          `Failed to collect payment: ${httpError.message}`,
        );
      }

      return payment;
    } catch (error) {
      this.logger.error('Payment request failed', error);
      if (error instanceof HttpException) {
        throw error;
      }

      handlePrismaError(error);
    }
  }

  async initiatePayment(
    { orderId, from }: { orderId: string; from: string },
    userId: string,
  ) {
    // validate users number
    let payment: Payment;
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new BadRequestException('User not found');
      const order = await this.ordersRepository.findOne(orderId);
      if (!order) throw new NotFoundException('Order not found');
      if (order.customer.id !== userId)
        throw new BadRequestException('Order does not belong to this user');

      if (order.isPayed) throw new BadRequestException('Order already paid');
      await this.validatePaymentInfo(userId, from);

      const res = await campayHttp.post('/collect/', {
        amount: Math.round(parseFloat(order.totalAmount.toString())),
        from,
        description: `Order payment for ${order.customer.fullname}`,
        external_reference: `ORDER_${Date.now()}`,
        extra_email: user.email,
      });

      if (res.status === 200) {
        payment = await this.paymentRepository.create({
          paymentId: res.data.reference,
          amount: parseFloat(order.totalAmount.toString()),
          currency: 'XAF',
          gateway: 'CAMPAY',
          phoneNumber: from.split('+')[1],
          ref: `ORDER_${Date.now()}_${userId}`,
          description: `Order payment for ${order.customer.fullname}`,
          status: 'PENDING',
          userEmail: userId,
          transactionUUID: res.data.reference,
          user: {
            connect: { id: userId },
          },
          order: {
            connect: { id: orderId },
          },
        });
      }

      if (payment) {
        await this.notificationService.sendSingleNotification(
          userId,
          'Payment Initiated',
          `Payment of ${order.totalAmount} XAF has been initiated. Please wait for the payment to be processed.`,
          {
            type: 'PAYMENT_INITIATED',
            amount: order.totalAmount.toString(),
            paymentId: res.data.reference,
          },
        );
      }

      return payment;
    } catch (error) {
      if (error instanceof AxiosError)
        throw new BadRequestException(error.response.data.message);
      this.sendFailedPaymentEmailToAdmins(
        `Failed to collect payment: ${error.message}`,
      );
      handlePrismaError(error);
    }
  }

  async collectPayment({
    amount,
    from,
    description,
    external_reference,
  }: CreateCampayDto) {
    try {
      const res = await campayHttp.post('/collect/', {
        amount: Math.round(parseFloat(amount)),
        from,
        description,
        external_reference,
      });

      return res.data;
    } catch (error) {
      if (error instanceof AxiosError) throw new BadRequestException(error);
      this.sendFailedPaymentEmailToAdmins(
        `Failed to collect payment: ${error.message}`,
      );
    }
  }

  async getTransactionStatus(paymentId: string) {
    try {
      const payment = await this.paymentRepository.findByGateway(
        paymentId,
        'CAMPAY',
      );
      if (!payment) throw new NotFoundException('Payment not found');

      const res = await campayHttp.get<GetTransactionResponse>(
        `/transaction/${paymentId}/`,
      );
      const status = res.data.status.toUpperCase();
      if (res.status === 2000) {
        await this.paymentRepository.update(payment.id, {
          status,
        });
      }
      return { data: res.data, payment };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async validateSignature(signature: string): Promise<boolean> {
    try {
      const webhookKey = this.configService.get<string>(
        'CAMPAY_WEBHOOK_SECRET',
      );

      if (!webhookKey) {
        this.logger.error('Webhook key not configured');
        return false;
      }

      // Verify JWT token
      const decoded = verify(signature, webhookKey);

      return !!decoded;
    } catch (error) {
      this.logger.error(`Failed to validate signature: ${error.message}`);
      return false;
    }
  }

  private async notifyDriver(
    driverId: string,
    orderId: string,
    deliveryId: string,
  ) {
    return this.notificationService.sendSingleNotification(
      driverId,
      'New Delivery Assignment',
      `You have been assigned a new delivery order #${orderId}`,
      {
        type: 'DELIVERY_ASSIGNED',
        orderId,
        deliveryId,
      },
    );
  }

  async handleWebhook(webhookData: any) {
    try {
      verify(webhookData.signature, this.serviceSecret);

      const payment = await this.paymentRepository.updateByPaymentId(
        webhookData.reference,
        {
          status: webhookData.status,
          ref: webhookData.operator_reference,
          channel: webhookData.operator,
        },
      );

      // If payment successful, update order status
      if (webhookData.status === 'SUCCESSFUL') {
        await this.paymentRepository.updateByPaymentId(webhookData.reference, {
          status: 'SUCCESSFUL',
        });

        const order = await this.ordersRepository.update(payment.order.id, {
          status: 'ASSIGNED',
          isPayed: true,
        });

        if (order.delivery) {
          await this.deliveryRepository.update(order.delivery.id, {
            status: 'ASSIGNED',
          });

          const driver = await this.driversRepository.findOne(
            order.delivery.currentDriver.id,
          );

          if (driver) {
            await this.driversRepository.update(driver.id, {
              currentStatus: 'ON_ERRAND',
              isAvailable: false,
            });

            await this.notifyDriver(driver.id, order.id, order.delivery.id);
          }
          await this.notificationService.sendSingleNotification(
            order.customer.id,
            'Payment Successfull',
            `Payment of ${payment.amount} XAF for order #${payment.order.id} has been recieved `,
            {
              type: 'SUCCESS_PAYMENT',
              paymentId: payment.paymentId,
            },
          );
        }
        await this.notifyAdminsOfSuccessfulPayment(payment, order);
      } else {
        await this.notificationService.sendSingleNotification(
          payment.user.id,
          'Payment Failed',
          `Payment of ${payment.amount} XAF has failed. Please try again`,
          {
            type: 'PAYMENT_FAILED',
            paymentId: payment.paymentId,
          },
        );
      }

      return { success: true };
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid webhook signature');
      }
      throw error;
    }
  }

  async processTransaction(transactionData: TransactionData) {
    const { status, reference, endpoint } = transactionData;

    this.logger.debug(
      `Processing ${endpoint} transaction: ${reference} with status: ${status}`,
    );

    try {
      // Determine if this is a collect or withdraw operation
      if (endpoint === 'collect') {
        return this.handleDeposit(transactionData);
      } else if (endpoint === 'withdraw') {
        return this.handleWithdrawal(transactionData);
      } else {
        this.logger.warn(`Unknown endpoint: ${endpoint}`);
        return { success: false, message: 'Unknown transaction type' };
      }
    } catch (error) {
      this.logger.error(
        `Error processing transaction ${reference}: ${error.message}`,
      );
      return { success: false, message: 'Failed to process transaction' };
    }
  }

  private async handleDeposit(transactionData: TransactionData) {
    const { status, reference } = transactionData;

    if (status === 'SUCCESSFUL') {
      this.handleSuccessfulPayment(transactionData);
      return { success: true, message: 'Deposit processed successfully' };
    } else if (status === 'FAILED') {
      const payment = await this.paymentRepository.findByPaymentId(reference);

      if (!payment) {
        this.logger.error(`Payment with ${reference} not found`);
        throw new NotFoundException(`Payment with ${reference} not found`);
      }

      this.handleFailedPayment(payment, transactionData);

      return { success: false, message: 'Failed deposit recorded' };
    }

    return { success: false, message: 'Unknown status' };
  }

  private async handleWithdrawal(transactionData: TransactionData) {
    const { status, reference, amount, currency } = transactionData;

    if (status === 'SUCCESSFUL') {
      // Process successful withdrawal
      this.logger.log(
        `Successful withdrawal of ${amount} ${currency}, reference: ${reference}`,
      );

      // Here you would typically:
      // 1. Update your database with the transaction status
      // 2. Finalize the withdrawal in your system
      // 3. Notify the user of successful withdrawal

      return { success: true, message: 'Withdrawal processed successfully' };
    } else if (status === 'FAILED') {
      // Handle failed withdrawal
      this.logger.warn(`Failed withdrawal, reference: ${reference}`);

      // Here you would typically:
      // 1. Update your database with the failed status
      // 2. Return funds to the user's account
      // 3. Notify the user about the failure

      return { success: false, message: 'Failed withdrawal recorded' };
    }

    return { success: false, message: 'Unknown status' };
  }

  async retryPayment(orderId: string) {
    try {
      const order = await this.ordersRepository.findOne(orderId);
      if (!order) throw new NotFoundException('Order not found');
      if (order.payment && order.payment.status === 'PENDING')
        throw new BadRequestException('Order already has a pending payment');

      if (order.payment && order.payment.status === 'SUCCESSFUL')
        throw new BadRequestException('Order already paid');

      const res = await campayHttp.post('/collect/', {
        amount: Math.round(order.totalAmount),
        from: order.payment.phoneNumber,
        description: `Order payment for ${order.customer.fullname}`,
        external_reference: `ORDER_${Date.now()}`,
        extra_email: order.customer.email,
      });

      if (res.status === 200) {
        await this.paymentRepository.update(order.payment.id, {
          paymentId: res.data.reference,
          status: 'PENDING',
          transactionUUID: res.data.reference,
        });
      }

      return res.data;
    } catch (error) {
      if (error instanceof AxiosError)
        throw new BadRequestException(error.response.data.message);
      handlePrismaError(error);
    }
  }

  async getPaymentStats(): Promise<any> {
    try {
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = new Date();
      return this.paymentRepository.getStatistics(startDate, endDate);
    } catch (error) {
      this.logger.error(`Error getting payment stats: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to retrieve payment statistics',
      );
    }
  }

  async getTotalAmount(): Promise<any> {
    try {
      const result = await this.paymentRepository.getTotalAmount();

      return {
        totalAmount: result._sum.amount || 0,
        currency: 'XAF',
      };
    } catch (error) {
      this.logger.error(`Error getting total amount: ${error.message}`);
      throw new InternalServerErrorException(
        'Failed to retrieve total payment amount',
      );
    }
  }

  private async notifyAdminsOfSuccessfulPayment(payment: any, order: any) {
    const admins = await this.usersRepository.getAdmins();

    if (admins.length > 0) {
      await this.notificationService.sendBulkNotifications(
        admins.map((admin) => admin.id),
        'New Payment Received 💰',
        `Payment of ${payment.amount} XAF received for Order #${order.id}`,
        {
          type: 'NEW_PAYMENT',
          orderId: order.id,
          paymentId: payment.paymentId,
          amount: payment.amount.toString(),
        },
      );
    }
  }

  private async handleDeliveryCancellation(
    delivery: Partial<Delivery>,
    order: Orders,
  ) {
    await this.notificationService.sendSingleNotification(
      delivery.currentDriver.user.id,
      'Delivery Cancelled',
      `Delivery for order #${order.id} has been cancelled due to failed payment.`,
      {
        type: 'DELIVERY_CANCELLED',
        orderId: order.id,
      },
    );

    await this.driversRepository.update(delivery.currentDriver.id, {
      currentStatus: 'AVAILABLE',
      isAvailable: true,
      updatedAt: new Date(),
    });

    await this.deliveryRepository.update(delivery.id, {
      currentDriver: { disconnect: true },
      status: 'PENDING_PAYMENT',
      updatedAt: new Date(),
    });
  }

  private async updateDeliveryForSuccessfulPayment(order: Orders) {
    await this.deliveryRepository.update(order.delivery.id, {
      status: 'ASSIGNED',
      updatedAt: new Date(),
    });

    if (order.delivery.currentDriver.id) {
      const driver = await this.driversRepository.findOne(
        order.delivery.currentDriver.id,
      );
      if (driver) {
        await this.driversRepository.update(driver.id, {
          currentStatus: 'ON_ERRAND',
          isAvailable: false,
          updatedAt: new Date(),
        });

        await this.notifyDriver(driver.user.id, order.id, order.delivery.id);
        this.logger.log(
          `Driver ${driver.id} assigned to delivery ${order.delivery.id}`,
        );
      }
    }
  }

  private async handleSuccessfulPayment(transactionData: any) {
    const { reference } = transactionData;

    this.logger.log(`Processing successful payment: ${reference}`);

    try {
      // check if payment exists
      const payment = await this.paymentRepository.findByPaymentId(reference);
      if (!payment) {
        this.logger.error(`Payment with ${reference} not found`);
        throw new NotFoundException(`Payment with ${reference} not found`);
      }
      // update payment status
      await this.paymentRepository.updateByPaymentId(reference, {
        status: 'SUCCESSFUL',
      });
      // Update order status
      const order = await this.ordersRepository.update(payment.order.id, {
        status: 'ASSIGNED',
        isPayed: true,
        updatedAt: new Date(),
      });

      // Handle delivery assignment
      if (order.delivery) {
        await this.updateDeliveryForSuccessfulPayment(order);
      }

      // Send success notification to customer
      await this.notificationService.sendSingleNotification(
        payment.user.id,
        'Payment Successful',
        `Payment of ${payment.amount} XAF for order #${order.id} has been received successfully.`,
        {
          type: 'PAYMENT_SUCCESS',
          paymentId: payment.paymentId,
          orderId: order.id,
          amount: payment.amount.toString(),
        },
      );

      await this.emailService.sendPaymentConfirmationEmail(
        payment.userEmail,
        order,
      );

      // Notify admins
      await this.notifyAdminsOfSuccessfulPayment(payment, order);

      this.logger.log(`Successfully processed payment: ${reference}`);
    } catch (error) {
      this.logger.error(
        `Error handling successful payment ${reference}:`,
        error,
      );
      // Consider sending alert to admins about processing error
      throw error;
    }
  }

  private async handleFailedPayment(payment: Payment, webhookData: any) {
    const { reference } = webhookData;

    this.logger.warn(`❌ Processing failed payment: ${reference}`);

    try {
      // Store failure reason from webhook
      await this.paymentRepository.updateByPaymentId(reference, {
        status: 'FAILED',
        failureReason:
          webhookData?.message ||
          webhookData?.reason ||
          'Payment failed via webhook',
      });

      // Handle delivery cancellation if driver was assigned
      const order = await this.ordersRepository.findOne(payment.order.id);
      if (order.delivery?.currentDriver.id) {
        const delivery = await this.deliveryRepository.findOne(
          order.delivery.id,
        );
        await this.handleDeliveryCancellation(delivery, order);
        this.logger.log(`Driver released due to failed payment: ${reference}`);
      }

      // Send failure notification with retry option
      await this.notificationService.sendSingleNotification(
        payment.user.id,
        'Payment Failed ⚠️',
        `Your payment of ${payment.amount} XAF has failed. You can retry the payment from your order history.`,
        {
          type: 'PAYMENT_FAILED',
          paymentId: payment.paymentId,
          orderId: payment.order.id,
          // canRetry: payment.retryCount < 3,
          reference: reference,
        },
      );

      this.logger.log(
        `❌ Failed payment ${reference} processed and notifications sent`,
      );
    } catch (error) {
      await this.sendFailedPaymentEmailToAdmins(
        `Failed payment ${reference} not processed: ${error.message}`,
      );
      this.logger.error(`Error handling failed payment ${reference}:`, error);
      throw error;
    }
  }

  private async sendFailedPaymentEmailToAdmins(message: string) {
    const admins = await this.usersRepository.getAdmins();
    const adminEmails = admins.map((admin) => admin.email);

    adminEmails.forEach(async (email) => {
      try {
        await this.emailService.sendBugReportEmail(
          email,
          message,
          'Failed Payment Notification',
        );
      } catch (error) {
        this.logger.error(`Failed to send email to ${email}:`, error);
      }
    });
  }

  private generateUniquePaymentId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const prefix = 'PAY';

    return `${prefix}_${timestamp}_${random}`.toUpperCase();
  }
}
