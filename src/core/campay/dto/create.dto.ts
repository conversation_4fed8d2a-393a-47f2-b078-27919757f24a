import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Orders } from 'src/common/schemas';

export class CreateCampayDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  amount: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  from: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  external_reference?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  order: Orders;

  @ApiProperty({ required: false })
  @IsString()
  email?: string;
}

export class RetryPaymentDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  orderId: string;
}
