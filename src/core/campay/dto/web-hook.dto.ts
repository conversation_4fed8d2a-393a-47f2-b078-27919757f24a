import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CampayWebhookDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  reference?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  amount?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  operator?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  operator_reference?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  endpoint?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  signature: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  external_reference?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  external_user?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  extra_first_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  extra_last_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  extra_email?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  app_amount?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  phone_number?: string;
}
