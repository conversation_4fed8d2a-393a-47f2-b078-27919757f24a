export interface CampayRequestBody {
  amount: string;
  from: string;
  description: string;
  external_reference: string;
}

export interface GetTransactionResponse {
  reference: string;
  status: string;
  amount: number;
  currency: string;
  operator: string;
  code: string;
  operator_reference: string;
}

export interface TransactionData {
  status: string;
  reference: string;
  amount: string;
  currency: string;
  operator: string;
  code: string;
  operatorReference: string;
  endpoint: string;
  externalReference: string;
  externalUser: string;
  extraFirstName: string;
  extraLastName: string;
  extraEmail: string;
  phoneNumber: string;
}
