import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import * as streamifier from 'streamifier';

import { TransformationOptions } from 'cloudinary';
import {
  configureCloudinary,
  defaultTransformation,
} from 'src/config/cloudinary.config';
import { CloudinaryResponse } from './cloudinary.entity';

@Injectable()
export class CloudinaryService {
  constructor() {
    configureCloudinary();
    this.validateConfiguration();
  }

  /**
   * Validate Cloudinary configuration on service initialization
   */
  private validateConfiguration() {
    const requiredEnvVars = [
      'CLOUDINARY_CLOUD_NAME',
      'CLOUDINARY_API_KEY',
      'CLOUDINARY_API_SECRET',
    ];

    const missingVars = requiredEnvVars.filter(
      (varName) => !process.env[varName],
    );

    if (missingVars.length > 0) {
      console.error('Missing Cloudinary environment variables:', missingVars);
      console.error(
        'Please ensure the following environment variables are set:',
      );
      missingVars.forEach((varName) => console.error(`- ${varName}`));
    } else {
      console.log('Cloudinary configuration validated successfully');
    }
  }

  async uploadFile(
    file: Express.Multer.File,
    folder: string,
  ): Promise<CloudinaryResponse> {
    return new Promise<CloudinaryResponse>((resolve, reject) => {
      // Validate file before upload
      if (!file || !file.buffer) {
        console.error('Invalid file provided to uploadFile:', file);
        return reject(
          new InternalServerErrorException(
            'Invalid file: File or file buffer is missing',
          ),
        );
      }

      console.log(`Attempting to upload file to Cloudinary folder: ${folder}`);
      console.log(`File details:`, {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
      });

      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder,
          transformation: defaultTransformation,
        },
        (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error);
            return reject(
              new InternalServerErrorException(
                `Failed to upload file to Cloudinary: ${error.message || 'Unknown error'}`,
              ),
            );
          }

          console.log('Cloudinary upload successful:', {
            public_id: result?.public_id,
            secure_url: result?.secure_url,
          });
          resolve(result as CloudinaryResponse);
        },
      );

      try {
        streamifier.createReadStream(file.buffer).pipe(uploadStream);
      } catch (streamError) {
        console.error('Stream error:', streamError);
        reject(
          new InternalServerErrorException(
            `Failed to create upload stream: ${streamError.message}`,
          ),
        );
      }
    });
  }

  async uploadFiles(
    files: Express.Multer.File[],
    folder: string,
  ): Promise<CloudinaryResponse[]> {
    return await Promise.all(
      files.map((file) => this.uploadFile(file, folder)),
    );
  }

  async uploadFromUrl(
    url: string,
    folder: string,
  ): Promise<CloudinaryResponse> {
    try {
      const result = await cloudinary.uploader.upload(url, {
        folder,
        transformation: defaultTransformation,
      });
      return result as CloudinaryResponse;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to upload file from URL to Cloudinary',
      );
    }
  }

  async deleteFile(publicId: string): Promise<void> {
    try {
      await cloudinary.uploader.destroy(publicId);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to delete file from Cloudinary',
      );
    }
  }

  checkPublicid(url: string): string | null {
    // Check if the URL is a Cloudinary URL
    if (!url || !url.includes('res.cloudinary.com')) {
      return null;
    }

    try {
      // Parse the URL to extract the public ID
      // Example: https://res.cloudinary.com/dqg4motqn/image/upload/v1749726930/employees/photos/oek7wkhdch7ksv9uv0zj.jpg
      const urlParts = url.split('/');

      // Find the upload index
      const uploadIndex = urlParts.findIndex((part) => part === 'upload');
      if (uploadIndex === -1) return null;

      // Skip version if present (starts with 'v' followed by numbers)
      let startIndex = uploadIndex + 1;
      if (urlParts[startIndex] && urlParts[startIndex].match(/^v\d+$/)) {
        startIndex++;
      }

      // Get the path after version (or after upload if no version)
      const pathParts = urlParts.slice(startIndex);
      if (pathParts.length === 0) return null;

      // Join the path parts and remove the file extension from the last part
      const lastPart = pathParts[pathParts.length - 1];
      const lastPartWithoutExtension = lastPart.split('.')[0];
      pathParts[pathParts.length - 1] = lastPartWithoutExtension;

      const publicId = pathParts.join('/');
      return publicId || null;
    } catch (error) {
      console.error('Error extracting public ID from URL:', error);
      return null;
    }
  }

  deleteManyImages(publicIds: string[]) {
    return Promise.all(publicIds.map((publicId) => this.deleteFile(publicId)));
  }

  /**
   * Check if a URL is a valid Cloudinary URL
   */
  isCloudinaryUrl(url: string): boolean {
    return !!(
      url &&
      url.includes('res.cloudinary.com') &&
      url.includes('/upload/')
    );
  }

  /**
   * Test Cloudinary connection and configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      // Try to get account details to test the connection
      const result = await cloudinary.api.ping();
      console.log('Cloudinary connection test successful:', result);
      return true;
    } catch (error) {
      console.error('Cloudinary connection test failed:', error);
      return false;
    }
  }

  async transformImage(
    publicId: string,
    transformation: TransformationOptions,
  ): Promise<CloudinaryResponse> {
    try {
      const result = await cloudinary.uploader.explicit(publicId, {
        type: 'upload',
        transformation: transformation,
      });
      return result as CloudinaryResponse;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to transform image on Cloudinary',
      );
    }
  }
}
