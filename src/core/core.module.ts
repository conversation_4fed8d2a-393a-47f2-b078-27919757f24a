import { Module } from '@nestjs/common';
import { PrismaModule } from './prisma/prisma.module';
import { EmailModule } from './email/email.module';
import { CloudinaryModule } from './cloudinary/cloudinary.module';
import { SmsModule } from './sms/sms.module';
import { CampayModule } from './campay/campay.module';

@Module({
  imports: [
    PrismaModule,
    EmailModule,
    CloudinaryModule,
    SmsModule,
    CampayModule,
  ],
})
export class CoreModule {}
