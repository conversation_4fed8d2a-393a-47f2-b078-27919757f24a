import { MailerService } from '@nestjs-modules/mailer';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailService {
  constructor(
    private readonly email: MailerService,
    private readonly configService: ConfigService,
  ) {}

  private imgUrl =
    'https://res.cloudinary.com/dqg4motqn/image/upload/v1754986125/zakaz_daqmc9.png';

  async sendUserOtp(
    email: string,
    username: string,
    otp: string,
    subject: string,
    message: string,
  ): Promise<{ success: boolean; messageId?: string }> {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: subject,
      template: './opt.template.ejs',
      context: {
        subject,
        email: username,
        otp,
        message,
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      console.log(
        `OTP email sent successfully to ${email}. Message ID: ${sent?.messageId || 'N/A'}`,
      );
      return { success: true, messageId: sent?.messageId };
    } catch (error) {
      console.error(`Failed to send OTP email to ${email}:`, error);
      throw new Error(`Email delivery failed: ${error.message}`);
    }
  }

  async sendOrderConfirmationEmail(email: string, order: any) {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Order Confirmation',
      template: './orders.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        customerName: order.customer.fullname,
        orderNumber: order.id,
        orderDate: order.createdAt,
        deliveryDate: order.delivery.createdAt,
        shippingAddress: order.delivery?.dropoffAddress?.label,
        items: order.items,
        totalAmount: order.totalAmount,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(sent?.messageId || 'Email sent');
    } catch (error) {
      Logger.error('Failed to send order email:', error);
    }
  }

  async sendPaymentConfirmationEmail(email: string, order: any) {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Payment Confirmation',
      template: './payment.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        customerName: order.customer.fullname,
        orderNumber: order.id,
        orderDate: order.createdAt,
        items: order.items,
        totalAmount: order.totalAmount,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(sent?.messageId || 'Email sent');
    } catch (error) {
      Logger.error('Failed to send payment confirmation email:', error);
    }
  }

  async sendBugReportEmail(email: string, message: string, title: string) {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: title,
      template: './admin.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        title,
        message,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(sent?.messageId || 'Email sent');
    } catch (error) {
      Logger.error('Failed to send bug report email:', error);
    }
  }

  async sendWelcomeEmail(email: string, fullname: string) {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Welcome to Zakaz',
      template: './welcome.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        customerName: fullname,
        companyName: 'Zakaz',
        headerTitle: 'Welcome to Zakaz',
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(sent?.messageId || 'Email sent');
    } catch (error) {
      Logger.error('Failed to send welcome email:', error);
    }
  }

  async sendDeleteRequest(email: string, token: string) {
    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Account Deletion Request',
      template: './opt.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        token,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(sent?.messageId || 'Email sent');
    } catch (error) {
      Logger.error('Failed to send delete request email:', error);
    }
  }

  async sendAdminCredentials(adminData: {
    email: string;
    fullname: string;
    password: string;
    phone?: string;
    permissions?: string[];
  }): Promise<{ success: boolean; messageId?: string }> {
    const { email, fullname, password, phone, permissions } = adminData;

    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Admin Account Created - Your Login Credentials',
      template: './admin-credentials.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail: this.configService.get('SUPPORT_EMAIL'),
        logoUrl: this.imgUrl,
        fullname,
        email,
        password,
        phone,
        permissions,
        loginUrl:
          this.configService.get('ADMIN_LOGIN_URL') ||
          `${this.configService.get('FRONTEND_URL')}/admin/login`,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(
        `Admin credentials email sent successfully to ${email}. Message ID: ${sent?.messageId || 'N/A'}`,
      );
      return { success: true, messageId: sent?.messageId };
    } catch (error) {
      Logger.error(
        `Failed to send admin credentials email to ${email}:`,
        error,
      );
      throw new Error(`Email delivery failed: ${error.message}`);
    }
  }

  async sendDriverCredentials(driverData: {
    email: string;
    fullname: string;
    password: string;
    licenseNumber?: string;
    vehicleType?: string;
    vehicleModel?: string;
  }): Promise<{ success: boolean; messageId?: string }> {
    const {
      email,
      fullname,
      password,
      licenseNumber,
      vehicleType,
      vehicleModel,
    } = driverData;

    const mailOptions = {
      from: `Support Team ${this.configService.get('APP_NAME')} <${this.configService.get('EMAIL_USER')}>`,
      to: email,
      subject: 'Driver Account Created - Welcome to Zakaz!',
      template: './driver-credentials.template.ejs',
      context: {
        appName: this.configService.get('APP_NAME'),
        supportEmail:
          this.configService.get('DRIVER_SUPPORT_EMAIL') ||
          this.configService.get('SUPPORT_EMAIL'),
        supportPhone: this.configService.get('DRIVER_SUPPORT_PHONE'),
        logoUrl: this.imgUrl,
        fullname,
        email,
        password,
        licenseNumber,
        vehicleType,
        vehicleModel,
        driverAppUrl:
          this.configService.get('DRIVER_APP_URL') ||
          `${this.configService.get('FRONTEND_URL')}/driver/download`,
      },
    };

    try {
      const sent = await this.email.sendMail(mailOptions);
      Logger.log(
        `Driver credentials email sent successfully to ${email}. Message ID: ${sent?.messageId || 'N/A'}`,
      );
      return { success: true, messageId: sent?.messageId };
    } catch (error) {
      Logger.error(
        `Failed to send driver credentials email to ${email}:`,
        error,
      );
      throw new Error(`Email delivery failed: ${error.message}`);
    }
  }
}
