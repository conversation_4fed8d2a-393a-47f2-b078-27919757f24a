<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Account Created - Zakaz</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
        }

        .logo {
            max-width: 150px;
            height: auto;
        }

        .content {
            padding: 30px 20px;
            line-height: 1.6;
        }

        .welcome-message {
            font-size: 20px;
            color: #2196F3;
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            background-color: #E3F2FD;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .credentials-box {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            font-family: 'Courier New', monospace;
        }

        .credential-item {
            margin: 15px 0;
            padding: 10px;
            background-color: #ffffff;
            border-radius: 4px;
            border-left: 3px solid #2196F3;
        }

        .credential-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 100px;
        }

        .credential-value {
            color: #333;
            font-weight: bold;
            word-break: break-all;
        }

        .permissions-section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 8px;
        }

        .permissions-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .permission-item {
            background-color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
            font-size: 14px;
        }

        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .security-notice h3 {
            color: #856404;
            margin-top: 0;
        }

        .security-notice ul {
            color: #856404;
            margin: 10px 0;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
            margin-top: 30px;
        }

        .support-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }

        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .permissions-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <% if (typeof logoUrl !== 'undefined' && logoUrl) { %>
                <img src="<%= logoUrl %>" alt="<%= appName || 'Zakaz' %>" class="logo" />
            <% } %>
            <h1>Admin Account Created</h1>
        </div>
        
        <div class="content">
            <p class="greeting">Hello <%= fullname %>,</p>
            
            <div class="welcome-message">
                🎉 Your Admin Account Has Been Created!
            </div>
            
            <p>Welcome to the Zakaz admin team! Your administrator account has been successfully created with full access to the platform.</p>
            
            <div class="credentials-box">
                <h3 style="margin-top: 0; color: #2196F3;">🔐 Your Login Credentials</h3>
                
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value"><%= email %></span>
                </div>
                
                <div class="credential-item">
                    <span class="credential-label">Password:</span>
                    <span class="credential-value"><%= password %></span>
                </div>
                
                <% if (typeof phone !== 'undefined' && phone) { %>
                <div class="credential-item">
                    <span class="credential-label">Phone:</span>
                    <span class="credential-value"><%= phone %></span>
                </div>
                <% } %>
            </div>

            <% if (typeof permissions !== 'undefined' && permissions && permissions.length > 0) { %>
            <div class="permissions-section">
                <h3 style="margin-top: 0; color: #2196F3;">🛡️ Your Admin Permissions</h3>
                <p>You have been granted the following administrative permissions:</p>
                
                <div class="permissions-list">
                    <% permissions.forEach(function(permission) { %>
                        <div class="permission-item">
                            ✓ <%= permission.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) %>
                        </div>
                    <% }); %>
                </div>
            </div>
            <% } %>

            <div class="security-notice">
                <h3>🔒 Important Security Information</h3>
                <ul>
                    <li><strong>Change your password immediately</strong> after your first login</li>
                    <li>Never share your credentials with anyone</li>
                    <li>Use a strong, unique password</li>
                    <li>Enable two-factor authentication if available</li>
                    <li>Log out completely when finished</li>
                </ul>
            </div>

            <p style="text-align: center; margin: 30px 0;">
                <a href="<%= loginUrl || '#' %>" class="btn">Login to Admin Panel</a>
            </p>
            
            <p>If you have any questions about your new admin account or need assistance, please don't hesitate to contact our support team.</p>
        </div>
        
        <div class="footer">
            <p><strong>Welcome to the Zakaz Admin Team!</strong></p>
            <p>Best regards,<br>
            The Zakaz Development Team</p>
            
            <div class="support-info">
                <p>Need help? Contact our support team:</p>
                <p><a href="mailto:<%= supportEmail || '<EMAIL>' %>"><%= supportEmail || '<EMAIL>' %></a></p>
            </div>
        </div>
    </div>
</body>
</html>
