<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        .email-wrapper {
            width: 100%;
            background-color: #f8f9fa;
            padding: 20px 0;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            text-align: center;
        }
        
        .header img {
            max-width: 150px;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        
        .header h1 {
            color: #ffffff;
            margin: 15px 0 0 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .content {
            padding: 40px 30px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .content p {
            margin: 0 0 20px 0;
            color: #555555;
        }
        
        .content .greeting {
            font-size: 18px;
            font-weight: 500;
            color: #333333;
        }
        
        .message-box {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .message-box p {
            margin: 0;
            color: #444444;
            font-size: 16px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        
        .footer .company-name {
            font-weight: 600;
            color: #495057;
            font-size: 16px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 30px 0;
        }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 10px;
            }
            
            .container {
                border-radius: 8px;
            }
            
            .header {
                padding: 25px 15px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 25px 20px;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .container {
                background-color: #1a1a1a;
            }
            
            .content p {
                color: #cccccc;
            }
            
            .content .greeting {
                color: #ffffff;
            }
            
            .message-box {
                background-color: #2d2d2d;
            }
            
            .message-box p {
                color: #dddddd;
            }
            
            .footer {
                background-color: #2d2d2d;
            }
        }
    </style>
</head>
<body>
    <!--[if mso]>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td>
    <![endif]-->
    
    <div class="email-wrapper">
        <div class="container">
            <div class="header">
                <% if (typeof logoUrl !== 'undefined' && logoUrl) { %>
                    <img src="<%= logoUrl %>" alt="<%= companyName || 'Company Logo' %>" />
                <% } %>
                <% if (typeof headerTitle !== 'undefined' && headerTitle) { %>
                    <h1><%= headerTitle %></h1>
                <% } %>
            </div>
            
            <div class="content">
                <p class="greeting">Hello <%= email %>,</p>
                
                <% if (typeof welcomeMessage !== 'undefined' && welcomeMessage) { %>
                    <p><%= welcomeMessage %></p>
                <% } else { %>
                    <p>Thank you for using <%= companyName || 'our service' %>!</p>
                <% } %>
                
                <% if (typeof message !== 'undefined' && message) { %>
                    <div class="message-box">
                        <p><%= message %></p>
                    </div>
                <% } %>
                
                <% if (typeof additionalContent !== 'undefined' && additionalContent) { %>
                    <div class="divider"></div>
                    <%- additionalContent %>
                <% } %>
                
                <p>If you have any questions or need assistance, please don't hesitate to reach out to our support team.</p>
            </div>
            
            <div class="footer">
                <p class="company-name"><%= companyName || 'Your Company Name' %></p>
                <p>Support Team</p>
                <p>
                    <a href="mailto:<%= supportEmail || '<EMAIL>' %>">
                        <%= supportEmail || '<EMAIL>' %>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!--[if mso]>
            </td>
        </tr>
    </table>
    <![endif]-->
</body>
</html>
