<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Account Created - <PERSON>akaz</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
        }

        .logo {
            max-width: 150px;
            height: auto;
        }

        .content {
            padding: 30px 20px;
            line-height: 1.6;
        }

        .welcome-message {
            font-size: 20px;
            color: #FF9800;
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            background-color: #FFF3E0;
            border-radius: 8px;
            border-left: 4px solid #FF9800;
        }

        .credentials-box {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            font-family: 'Courier New', monospace;
        }

        .credential-item {
            margin: 15px 0;
            padding: 10px;
            background-color: #ffffff;
            border-radius: 4px;
            border-left: 3px solid #FF9800;
        }

        .credential-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 120px;
        }

        .credential-value {
            color: #333;
            font-weight: bold;
            word-break: break-all;
        }

        .driver-info-section {
            margin: 25px 0;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 8px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            background-color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            font-size: 14px;
        }

        .info-value {
            color: #333;
            font-size: 16px;
            margin-top: 5px;
        }

        .features-section {
            margin: 25px 0;
            padding: 20px;
            background-color: #e8f5e9;
            border-radius: 8px;
        }

        .features-list {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #c8e6c9;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li:before {
            content: "🚗 ";
            margin-right: 10px;
        }

        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .security-notice h3 {
            color: #856404;
            margin-top: 0;
        }

        .security-notice ul {
            color: #856404;
            margin: 10px 0;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
            margin-top: 30px;
        }

        .support-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #FF9800;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }

        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <% if (typeof logoUrl !== 'undefined' && logoUrl) { %>
                <img src="<%= logoUrl %>" alt="<%= appName || 'Zakaz' %>" class="logo" />
            <% } %>
            <h1>Driver Account Created</h1>
        </div>
        
        <div class="content">
            <p class="greeting">Hello <%= fullname %>,</p>
            
            <div class="welcome-message">
                🚗 Welcome to the Zakaz Driver Team!
            </div>
            
            <p>Congratulations! Your driver account has been successfully created. You're now part of the Zakaz delivery network and ready to start earning by making deliveries.</p>
            
            <div class="credentials-box">
                <h3 style="margin-top: 0; color: #FF9800;">🔐 Your Login Credentials</h3>
                
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value"><%= email %></span>
                </div>
                
                <div class="credential-item">
                    <span class="credential-label">Password:</span>
                    <span class="credential-value"><%= password %></span>
                </div>
            </div>

            <div class="driver-info-section">
                <h3 style="margin-top: 0; color: #FF9800;">📋 Your Driver Information</h3>
                
                <div class="info-grid">
                    <% if (typeof licenseNumber !== 'undefined' && licenseNumber) { %>
                    <div class="info-item">
                        <div class="info-label">License Number</div>
                        <div class="info-value"><%= licenseNumber %></div>
                    </div>
                    <% } %>
                    
                    <% if (typeof vehicleType !== 'undefined' && vehicleType) { %>
                    <div class="info-item">
                        <div class="info-label">Vehicle Type</div>
                        <div class="info-value"><%= vehicleType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) %></div>
                    </div>
                    <% } %>
                    
                    <% if (typeof vehicleModel !== 'undefined' && vehicleModel) { %>
                    <div class="info-item">
                        <div class="info-label">Vehicle Model</div>
                        <div class="info-value"><%= vehicleModel %></div>
                    </div>
                    <% } %>
                    
                    <div class="info-item">
                        <div class="info-label">Account Status</div>
                        <div class="info-value" style="color: #4CAF50;">✓ Active & Verified</div>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <h3 style="margin-top: 0; color: #4CAF50;">🎯 What You Can Do</h3>
                <ul class="features-list">
                    <li>Accept and manage delivery requests</li>
                    <li>Track your earnings and performance</li>
                    <li>Update your availability status</li>
                    <li>Navigate to pickup and delivery locations</li>
                    <li>Communicate with customers</li>
                    <li>View your delivery history</li>
                    <li>Update your profile and vehicle information</li>
                </ul>
            </div>

            <div class="security-notice">
                <h3>🔒 Important Security & Safety Information</h3>
                <ul>
                    <li><strong>Change your password immediately</strong> after your first login</li>
                    <li>Keep your login credentials secure and private</li>
                    <li>Always verify delivery addresses before departing</li>
                    <li>Follow all traffic laws and safety guidelines</li>
                    <li>Contact support immediately if you encounter any issues</li>
                    <li>Keep your vehicle information up to date</li>
                </ul>
            </div>

            <p style="text-align: center; margin: 30px 0;">
                <a href="<%= driverAppUrl || '#' %>" class="btn">Download Driver App</a>
            </p>
            
            <p>Ready to start earning? Download the Zakaz Driver app and begin accepting delivery requests. Our support team is here to help you every step of the way!</p>
        </div>
        
        <div class="footer">
            <p><strong>Welcome to the Zakaz Driver Network!</strong></p>
            <p>Best regards,<br>
            The Zakaz Team</p>
            
            <div class="support-info">
                <p>Need help? Contact our driver support team:</p>
                <p><a href="mailto:<%= supportEmail || '<EMAIL>' %>"><%= supportEmail || '<EMAIL>' %></a></p>
                <% if (typeof supportPhone !== 'undefined' && supportPhone) { %>
                    <p>Phone: <%= supportPhone %></p>
                <% } %>
            </div>
        </div>
    </div>
</body>
</html>
