<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%= subject %></title>
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
    <![endif]-->
    <style>
      /* Reset styles for email clients */
      body,
      table,
      td,
      p,
      a,
      li,
      blockquote {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      table,
      td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }

      img {
        -ms-interpolation-mode: bicubic;
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
      }

      body {
        font-family: Arial, sans-serif;
        color: #333333;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
        width: 100% !important;
        min-width: 100%;
      }

      .email-container {
        max-width: 600px;
        margin: 20px auto;
        background-color: #ffffff;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        text-align: center;
        padding: 30px 20px;
        background-color: #ffffff;
      }

      .logo {
        display: inline-block;
        max-width: 150px;
        background: #ffffff;
        border-radius: 20px; /* Fixed missing px unit */
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .logo img {
        max-width: 100%;
        height: auto;
        display: block;
      }

      .content {
        padding: 0 30px 30px 30px;
      }

      .greeting {
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 20px;
      }

      .message {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 30px;
        color: #555555;
      }

      .otp-container {
        background-color: #f8f9fa;
        border: 2px dashed #4caf50;
        border-radius: 12px;
        padding: 25px;
        text-align: center;
        margin: 30px 0;
      }

      .otp-label {
        font-size: 14px;
        color: #666666;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .otp-code {
        font-size: 32px;
        font-weight: bold;
        color: #4caf50;
        letter-spacing: 4px;
        font-family: 'Courier New', monospace;
      }

      .thank-you {
        font-size: 16px;
        margin-bottom: 20px;
        color: #333333;
      }

      .disclaimer {
        font-size: 14px;
        color: #777777;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        padding: 15px;
        margin-top: 20px;
      }

      .footer {
        background-color: #f8f9fa;
        font-style: italic;
        padding: 25px 30px;
        text-align: center;
        border-top: 1px solid #e9ecef;
      }

      .footer p {
        margin: 5px 0;
        font-size: 14px;
        color: #666666;
      }

      .footer a {
        color: #4caf50;
        text-decoration: none;
      }

      .footer a:hover {
        text-decoration: underline;
      }

      /* Mobile responsiveness */
      @media only screen and (max-width: 600px) {
        .email-container {
          margin: 10px;
          border-radius: 0;
        }

        .content,
        .footer {
          padding-left: 20px;
          padding-right: 20px;
        }

        .otp-code {
          font-size: 28px;
          letter-spacing: 2px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Wrapped everything in a table for better email client compatibility -->
    <table
      role="presentation"
      cellspacing="0"
      cellpadding="0"
      border="0"
      width="100%"
    >
      <tr>
        <td>
          <div class="email-container">
            <header class="header">
              <div class="logo">
                <img
                  src="https://res.cloudinary.com/dqg4motqn/image/upload/v1754986125/zakaz_daqmc9.png"
                  alt="<%= appName %> Logo"
                  width="100"
                />
              </div>
            </header>

            <div class="content">
              <p class="greeting">Hello <%= email %>!</p>
              <p class="message"><%= message %></p>

              <!-- Enhanced OTP display with better visual hierarchy -->
              <div class="otp-container">
                <div class="otp-label">Your Verification Code</div>
                <div class="otp-code"><%= otp %></div>
              </div>

              <p class="thank-you">Thank you for using <%= appName %>!</p>

              <!-- Enhanced disclaimer with better visibility -->
              <div class="disclaimer">
                <strong>Security Notice:</strong> If you did not request this
                verification code, please ignore this email. Your account
                remains secure. This code will expire in 10 minutes.
              </div>
            </div>

            <!-- Moved footer inside container for better layout -->
            <footer class="footer">
              <p><strong>Best regards,</strong></p>
              <p><%= appName %> Support Team</p>
              <p>
                Need help? Contact us at
                <a href="mailto:<%= supportEmail %>"><%= supportEmail %></a>
              </p>
            </footer>
          </div>
        </td>
      </tr>
    </table>
  </body>
</html>
