<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles for email clients */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* Main styles */
        body {
            font-family: Arial, sans-serif;
            color: #333333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
        }
        
        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #f4f4f4;
        }
        
        .email-content {
            background-color: #ffffff;
            margin: 20px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            padding: 30px 20px;
            background-color: #ffffff;
        }
        
        .content {
            padding: 0 30px 20px 30px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .order-details {
            margin: 25px 0;
        }
        
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid #e0e0e0;
        }
        
        .order-table th {
            background-color: #4CAF50;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }
        
        .order-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .order-table tr:last-child td {
            border-bottom: none;
        }
        
        .order-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .total-row {
            background-color: #f8f8f8 !important;
            font-weight: 600;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background-color: #f8f8f8;
            font-size: 14px;
            color: #666666;
        }
        
        .footer a {
            color: #4CAF50;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .company-name {
            font-weight: 600;
            color: #333333;
        }
        
        .order-number {
            background-color: #f0f8f0;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        /* Mobile responsive */
        @media only screen and (max-width: 600px) {
            .email-content {
                margin: 10px;
            }
            .content {
                padding: 0 20px 20px 20px;
            }
            .order-table th,
            .order-table td {
                padding: 8px 6px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!--[if mso]>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td>
    <![endif]-->
    
    <div class="email-container">
        <div class="email-content">
            <!-- Header -->
            <div class="header">
                <img src="<%= logoUrl %>" alt="<%= appName %>" width="100" height="auto" style="max-width: 150px;">
            </div>
            
            <!-- Content -->
            <div class="content">
                <h2 style="color: #333333; margin-bottom: 20px;">Order Confirmation</h2>
                
                <p>Hello <%= customerName %>,</p>
                
                <p>Thank you for your order with <span class="company-name"><%= appName %></span>! We're excited to confirm that your order has been successfully processed.</p>
                
                <div class="order-number">
                    <strong>Order Number:</strong> <%= orderNumber %><br>
                    <strong>Order Date:</strong> <%= orderDate %>
                </div>
                
                <!-- Order Details -->
                <div class="order-details">
                    <h3 style="color: #333333; margin-bottom: 15px;">Order Details</h3>
                    
                    <table class="order-table" role="presentation">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% orderItems.forEach(function(item) { %>
                            <tr>
                                <td><%= item.name %></td>
                                <td><%= item.quantity %></td>
                                <td>$<%= item.price %></td>
                                <td>$<%= item.total %></td>
                            </tr>
                            <% }); %>
                            
                            <!-- Totals -->
                            <tr class="total-row">
                                <td colspan="3"><strong>Subtotal:</strong></td>
                                <td><strong>$<%= subtotal %></strong></td>
                            </tr>
                            <% if (shippingCost) { %>
                            <tr class="total-row">
                                <td colspan="3"><strong>Shipping:</strong></td>
                                <td><strong>$<%= shippingCost %></strong></td>
                            </tr>
                            <% } %>
                            <% if (taxAmount) { %>
                            <tr class="total-row">
                                <td colspan="3"><strong>Tax:</strong></td>
                                <td><strong>$<%= taxAmount %></strong></td>
                            </tr>
                            <% } %>
                            <tr class="total-row" style="background-color: #4CAF50; color: white;">
                                <td colspan="3"><strong>Total:</strong></td>
                                <td><strong>$<%= totalAmount %></strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <p><strong>Estimated Delivery:</strong> <%= deliveryDate %></p>
                <p><strong>Shipping Address:</strong><br>
                <%= shippingAddress %></p>
                
                <p>We'll send you another email with tracking information once your order ships.</p>
                
                <p>Thank you for choosing <%= appName %>!</p>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <p><strong><%= appName %></strong><br>
                Support Team</p>
                
                <p>Need help? Contact us at <a href="mailto:<%= supportEmail %>"><%= supportEmail %></a></p>
                
                <p style="font-size: 12px; color: #999999; margin-top: 20px;">
                    This email was sent to <%= customerEmail %>. If you have any questions about your order, please don't hesitate to contact us.
                </p>
            </div>
        </div>
    </div>
    
    <!--[if mso]>
            </td>
        </tr>
    </table>
    <![endif]-->
</body>
</html>
