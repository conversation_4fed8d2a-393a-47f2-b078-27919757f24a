<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 30px -20px;
        }
        .header img {
            max-width: 150px;
            height: auto;
        }
        .header h1 {
            color: white;
            margin: 15px 0 0 0;
            font-size: 24px;
            font-weight: bold;
        }
        .content {
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.6;
        }
        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .success-message {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            text-align: center;
            font-weight: 500;
        }
        .order-details {
            margin: 30px 0;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .order-details table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .order-details th,
        .order-details td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .order-details th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        .order-details td {
            font-size: 15px;
        }
        .order-details tr:last-child td {
            border-bottom: none;
        }
        .order-details tr:nth-child(even) {
            background-color: #fafafa;
        }
        .price-cell {
            font-weight: 600;
            color: #4CAF50;
        }
        .total-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            font-size: 16px;
        }
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
            border-top: 2px solid #4CAF50;
            padding-top: 12px;
            margin-top: 15px;
        }
        .footer {
            text-align: center;
            font-size: 14px;
            color: #777;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        .footer a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .support-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .header h1 {
                font-size: 20px;
            }
            .order-details th,
            .order-details td {
                padding: 10px 8px;
                font-size: 14px;
            }
            .total-row {
                font-size: 14px;
            }
            .total-row.final {
                font-size: 16px;
            }
        }
        
        /* Outlook specific styles */
        /* <!--[if mso]>
        .container {
            width: 600px !important;
        }
        <![endif] --> */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <% if (typeof logoUrl !== 'undefined' && logoUrl) { %>
                <img src="<%= logoUrl %>" alt="<%= appName || 'Company Logo' %>" width="90" height="auto">
            <% } %>
            <h1>Payment Confirmed</h1>
        </div>
        
        <div class="content">
            <p class="greeting">Hello <%= customerName || email %>,</p>
            
            <div class="success-message">
                ✅ Your payment has been successfully processed!
            </div>
            
            <p>Thank you for using <%= appName || 'our service' %>! Your order has been confirmed Delivery is in progress.</p>
            
            <% if (typeof orderNumber !== 'undefined' && orderNumber) { %>
                <p><strong>Order Number:</strong> #<%= orderNumber %></p>
            <% } %>
            
            <% if (typeof orderDate !== 'undefined' && orderDate) { %>
                <p><strong>Order Date:</strong> <%= orderDate %></p>
            <% } %>
            
            <div class="order-details">
                <table>
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (typeof orderItems !== 'undefined' && orderItems && orderItems.length > 0) { %>
                            <% orderItems.forEach(function(item) { %>
                                <tr>
                                    <td><%= item.name || item.product?.name || 'Product' %></td>
                                    <td><%= item.quantity || 1 %></td>
                                    <td class="price-cell">$<%= (item.unitPrice || item.price || 0).toFixed(2) %></td>
                                    <td class="price-cell">$<%= ((item.quantity || 1) * (item.unitPrice || item.price || 0)).toFixed(2) %></td>
                                </tr>
                            <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="4" style="text-align: center; color: #666;">No items found</td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
            
            <p>Your order will be processed within 1-2 business days. You'll receive a shipping confirmation email once your items are on their way.</p>
        </div>
        
        <div class="footer">
            <p><strong>Thank you for your business!</strong></p>
            <p>Best regards,<br>
            <%= appName || 'Your Application Name' %><br>
            Support Team</p>
            
            <div class="support-info">
                <p>Need help? Contact our support team:</p>
                <p><a href="mailto:<%= supportEmail || '<EMAIL>' %>"><%= supportEmail || '<EMAIL>' %></a></p>
                <% if (typeof supportPhone !== 'undefined' && supportPhone) { %>
                    <p>Phone: <%= supportPhone %></p>
                <% } %>
            </div>
        </div>
    </div>
</body>
</html>
