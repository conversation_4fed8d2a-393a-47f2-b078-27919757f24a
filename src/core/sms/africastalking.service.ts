import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AfricasTalking from 'africastalking';

@Injectable()
export class AfricaTalkingService {
  private africasTalking;

  constructor(private readonly configService: ConfigService) {
    this.africasTalking = AfricasTalking({
      apiKey: this.configService.get<string>('AFRICA_TALK_API_KEY'),
      username: 'Hello',
    });
  }

  async sendSms(phone: string, message: string, from?: string) {
    try {
      const result = await this.africasTalking.SMS.send({
        to: phone,
        message: message,
        from: from || undefined,
      });

      return result.data;
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Failed to send SMS via AfricasTalking',
      );
    }
  }

  async sendBulkSms(phones: string[], message: string, from?: string) {
    try {
      const result = await this.africasTalking.SMS.send({
        to: phones,
        message: message,
        from: from || undefined,
      });

      return result;
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Failed to send bulk SMS via AfricasTalking',
      );
    }
  }
}
