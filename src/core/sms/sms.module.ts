import { Global, Module } from '@nestjs/common';
import { TextbeltService } from './textbelt.service';
import { AfricaTalkingService } from './africastalking.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as <PERSON>wi<PERSON> from 'twilio';
import { TwilioService } from './twilio.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    TextbeltService,
    AfricaTalkingService,
    TwilioService,
    {
      provide: 'TWILIO',
      useFactory: (configService: ConfigService) => {
        return Twilio(
          configService.get('TWILIO_ACCOUNT_SID'),
          configService.get('TWILIO_AUTH_TOKEN'),
        );
      },
      inject: [ConfigService],
    },
  ],
  exports: [TextbeltService, AfricaTalkingService, TwilioService, 'TWILIO'],
})
export class SmsModule {}
