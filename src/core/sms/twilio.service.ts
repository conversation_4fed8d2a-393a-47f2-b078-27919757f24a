import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twi<PERSON> } from 'twilio';

@Injectable()
export class TwilioService {
  constructor(
    @Inject('TWILIO') private readonly twilioClient: Twilio,
    private configService: ConfigService,
  ) {}

  async sendSms(phone: string, message: string, from?: string) {
    try {
      const result = await this.twilioClient.messages.create({
        messagingServiceSid: this.configService.get<string>(
          'TWILIO_MESSAGING_SERVICE_SID',
        ),
        to: phone,
        body: message,
        from: from,
      });

      return result;
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Failed to send SMS via Twilio',
      );
    }
  }

  async sendBulkSms(phones: string[], message: string, from?: string) {
    try {
      const promises = phones.map((phone) =>
        this.twilioClient.messages.create({
          to: phone,
          body: message,
          from: from || process.env.TWILIO_PHONE_NUMBER,
        }),
      );

      const results = await Promise.all(promises);
      return results;
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Failed to send bulk SMS via Twilio',
      );
    }
  }
}
