import { Controller, Get, Logger } from '@nestjs/common';
import { HealthService } from './health.service';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

@Controller()
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(private readonly healthService: HealthService) {}

  @ApiTags('Health')
  @ApiOperation({ summary: 'Check health' })
  @Get('/')
  async checkHealth() {
    this.logger.log('Checking health');
    const databaseConnection =
      await this.healthService.checkDatabaseConnection();
    return {
      databaseConnection,
    };
  }
}
