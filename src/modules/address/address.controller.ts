import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AddressService } from './address.service';
import { CreateAddressDto } from './dto/create-address.dto';
import {
  UpdateAddressDto,
  UpdateAddressSwaggerDto,
} from './dto/update-address.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { User } from 'src/common/decorators/user.decorator';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';

@ApiTags('addresses')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('addresses')
export class AddressController {
  constructor(private readonly addressService: AddressService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new address' })
  @ApiResponse({
    status: 201,
    description: 'The address has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiQuery({ required: false, name: 'productSourceId' })
  create(
    @Query('productSourceId') productSourceId: string,
    @Body() createAddressDto: CreateAddressDto,
    @User() user,
  ) {
    return this.addressService.create(createAddressDto, user.userId);
  }

  @Get('my-addresses')
  @ApiOperation({ summary: 'Get all addresses for a user' })
  @ApiResponse({ status: 200, description: 'List of addresses for the user.' })
  findAll(@User() user) {
    return this.addressService.findAll(user.userId);
  }

  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @Get('all-addresses')
  @ApiOperation({ summary: 'Get all addresses for all users' })
  @ApiResponse({ status: 200, description: 'List of addresses for all users.' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  getAllUsersAddress(@Query() pagination: PaginationDto) {
    return this.addressService.getAllUsersAddress(pagination);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an address by ID' })
  @ApiResponse({ status: 200, description: 'The address with the given ID.' })
  @ApiResponse({ status: 404, description: 'Address not found.' })
  findOne(@Param('id') id: string) {
    return this.addressService.findOne(id);
  }

  @Patch(':id')
  @ApiBody({ type: UpdateAddressSwaggerDto })
  @ApiOperation({ summary: 'Update an address by ID' })
  @ApiResponse({
    status: 200,
    description: 'The address has been successfully updated.',
  })
  @ApiResponse({ status: 404, description: 'Address not found.' })
  @ApiQuery({ name: 'id', required: false })
  update(
    @Param('id') id: string,
    @Body() updateAddressDto: UpdateAddressDto,
    @User() user,
  ) {
    return this.addressService.update(id, updateAddressDto, user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an address by ID' })
  @ApiResponse({
    status: 204,
    description: 'The address has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Address not found.' })
  remove(@Param('id') id: string): Promise<void> {
    return this.addressService.remove(id);
  }

  @Patch(':id/set-default')
  @ApiOperation({ summary: 'Set an address as default' })
  @ApiResponse({
    status: 200,
    description: 'The address has been set as default.',
  })
  @ApiResponse({ status: 404, description: 'Address not found.' })
  setDefault(@Param('id') id: string, @User() user) {
    return this.addressService.setDefault(id, user.userId);
  }
}
