import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import { AddressRepository } from 'src/common/repositories';

@Injectable()
export class AddressService {
  constructor(private readonly addressRepository: AddressRepository) {}

  async create(createAddressDto: CreateAddressDto, userId?: string) {
    const {
      street,
      city,
      state,
      country,
      latitude,
      longitude,
      label,
      isDefault,
    } = createAddressDto;

    try {
      // If this address is set as default, unset any existing default address
      if (isDefault && userId) {
        await this.addressRepository.updateMany({ isDefault: false }, userId);
      }

      const data: any = {
        street,
        city,
        state,
        country,
        latitude,
        longitude,
        label,
        isDefault: isDefault || false,
      };
      return this.addressRepository.create({
        ...data,
        userId,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(
    id: string,
    updateAddressDto: UpdateAddressDto,
    userId?: string,
  ) {
    const {
      street,
      city,
      state,
      country,
      latitude,
      longitude,
      label,
      isDefault,
    } = updateAddressDto;

    try {
      // If this address is being set as default, unset any existing default address
      if (isDefault && userId) {
        await this.addressRepository.updateMany(
          { isDefault: false },
          userId,
          id,
        );
      }

      const updatedAddress = await this.addressRepository.update(id, {
        data: {
          street,
          city,
          state,
          country,
          latitude,
          longitude,
          label,
          isDefault,
        },
      });

      if (!updatedAddress)
        throw new NotFoundException(`Address with id ${id} not found`);

      return updatedAddress;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    const address = await this.addressRepository.findOne(id);
    if (!address) {
      throw new NotFoundException(`Address with id ${id} not found`);
    }
    return address;
  }

  async getAllUsersAddress(pagination: PaginationDto) {
    try {
      return this.addressRepository.getAllUsersAddress(pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll(userId: string) {
    try {
      return this.addressRepository.findAll(userId);
    } catch (err) {
      handlePrismaError(err);
    }
  }

  async setDefault(id: string, userId: string) {
    try {
      const defaultAddress = await this.addressRepository.setDefault(
        id,
        userId,
      );
      if (defaultAddress) {
        this.addressRepository.updateMany({ isDefault: false }, userId, id);
      }
      return defaultAddress;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string): Promise<void> {
    try {
      const address = await this.findOne(id);

      if (!address)
        throw new NotFoundException(`Address with id ${id} not found`);

      const deliveryWithAddress =
        await this.addressRepository.findAddressInDelivery(id);

      if (deliveryWithAddress) {
        throw new BadRequestException(
          'Cannot delete address that is being used in a delivery',
        );
      }

      await this.addressRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
