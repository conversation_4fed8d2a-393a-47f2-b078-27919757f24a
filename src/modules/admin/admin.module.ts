import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';

import { AdminsRepository, UsersRepository } from 'src/common/repositories';
import { EmailService } from 'src/core/email/email.service';

@Module({
  controllers: [AdminController],
  providers: [AdminService, AdminsRepository, UsersRepository, EmailService],
})
export class AdminModule {}
