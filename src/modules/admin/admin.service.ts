import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UserRole } from '@prisma/client';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { CreateAdminDto } from './dto/create-admin.dto';
import { UpdateAdminDto } from './dto/update-admin.dto';
import * as bcrypt from 'bcrypt';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { AdminsRepository, UsersRepository } from 'src/common/repositories';
import { EmailService } from 'src/core/email/email.service';

@Injectable()
export class AdminService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly adminsRepository: AdminsRepository,
    private readonly usersRepository: UsersRepository,
    private readonly emailService: EmailService,
  ) {}

  async create(createAdminDto: CreateAdminDto) {
    const { email, fullname, password, phone, permissions } = createAdminDto;
    const hashedPassword = await bcrypt.hash(password, 10);
    try {
      const existingUser = await this.usersRepository.getUserByEmail(email);
      if (existingUser) throw new ConflictException('Email already in use');
      const user = await this.usersRepository.create({
        email,
        fullname,
        password: hashedPassword,
        phone,
        isEmailVerified: true,
        isPhoneVerified: true,
        isVerified: true,
        role: UserRole.ADMIN,
        profile: {
          create: {
            gender: null,
            avatar: 'https://avatar.iran.liara.run/public/50',
            nickname: null,
          },
        },
        adminDetails: {
          create: {
            permissions,
          },
        },
      });

      // Send credentials email to the new admin
      try {
        await this.emailService.sendAdminCredentials({
          email,
          fullname,
          password, // Send the plain password before hashing
          phone,
          permissions,
        });
      } catch (emailError) {
        console.error('Failed to send admin credentials email:', emailError);
        // Don't throw here - admin creation succeeded, email is secondary
      }

      return user;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const user = await this.adminsRepository.findOne(id);
      if (!user) throw new NotFoundException(`Admin with ID ${id} not found`);
      return user;
    } catch (err) {
      handlePrismaError(err);
    }
  }

  async update(id: string, updateAdminDto: UpdateAdminDto) {
    try {
      const existingUser = await this.adminsRepository.findOne(id);
      if (!existingUser)
        throw new NotFoundException(`Admin with ID ${id} not found`);
      const user = await this.adminsRepository.update(id, updateAdminDto);
      return user;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      const user = await this.adminsRepository.findOne(id);
      if (!user) throw new NotFoundException(`Admin with ID ${id} not found`);
      await this.adminsRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
