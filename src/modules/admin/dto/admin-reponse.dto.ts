import { ApiProperty } from '@nestjs/swagger';
import { AdminPermission, UserRole } from '@prisma/client';

export class AdminResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  fullname: string;

  @ApiProperty({ required: false })
  phone?: string;

  @ApiProperty({ required: false })
  role: UserRole;

  @ApiProperty({ type: [String], enum: AdminPermission })
  permissions: AdminPermission[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
