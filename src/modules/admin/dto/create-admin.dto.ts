import { ApiProperty } from '@nestjs/swagger';
import { AdminPermission } from '@prisma/client';
import { IsString, IsOptional, IsEnum, IsArray } from 'class-validator';

export class CreateAdminDto {
  @ApiProperty()
  @IsString()
  email: string;
  @ApiProperty()
  @IsString()
  fullname: string;
  @ApiProperty()
  @IsString()
  password: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  phone?: string;
  @ApiProperty({ type: [String], enum: AdminPermission })
  @IsArray()
  @IsEnum(AdminPermission, { each: true })
  permissions: AdminPermission[];
}
