import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Req,
  Patch,
  Delete,
  Param,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalLoginDto } from './dto/local-login.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { SignUpDto } from './dto/sign-up.dto';
import { GoogleAuthGuard } from 'src/common/guards/google-auth.guard';
import { LocalAuthGuard } from 'src/common/guards/local-auth.guard';
import { OtpService } from './opt.service';
import { VerifyOtpDto } from './dto/verify-otp';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { GenerateOtpDto } from './dto/generate-otp.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { VerifyPhoneDto } from './dto/verify-phone.dto';
import { GeneratePhoneOtpDto } from './dto/generate-phone-otp.dto';
import { NotificationService } from '../notification/notification.service';
import { UpdateOauthAccount } from './dto/update-oauth-info';
import { TwilioService } from 'src/core/sms/twilio.service';
import { GoogleAuthMobileDto } from './dto/google-auth-mobile.dto';
import { PhonesService } from './phones.service';
import { UpdatePhoneDto } from './dto/update-phone.dto';

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private readonly otpService: OtpService,
    private readonly notification: NotificationService,
    private readonly twilioService: TwilioService,
    private readonly phonesService: PhonesService,
  ) {}

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @ApiOperation({
    summary: 'User Sign-In',
    description: 'Logs in the user with the provided credentials.',
  })
  @ApiBody({
    type: LocalLoginDto,
    description: 'Request body containing login credentials.',
  })
  async login(@Body() authDto: LocalLoginDto) {
    return this.authService.localLogin(authDto);
  }

  @Get('google')
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({
    summary: 'Authenticate via Google',
    description: 'Redirects to Google for OAuth2 authentication.',
  })
  @ApiOkResponse({
    description: 'Returns a message indicating authentication with Google.',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized if user cannot be authenticated by Google.',
  })
  async googleAuth() {
    return { message: 'Auth with Google initiated' };
  }

  @Get('google/redirect')
  @UseGuards(GoogleAuthGuard)
  async googleAuthRedirect(@Req() req: Request) {
    const { user } = req as any;

    return await this.authService.googleLogin({
      avatar: user.avatar,
      email: user.email,
      fullname: user.displayName,
      id: user.id,
      verified: user.verified,
    });
  }
  @Post('generate-otp')
  @ApiOperation({
    summary: 'generate otp',
    description: 'Sends OTP to the provided phone number or email.',
  })
  @ApiOkResponse({
    description: 'OTP sent successfully.',
  })
  @ApiBody({ type: GenerateOtpDto })
  @ApiResponse({
    status: 400,
    description: 'Bad Request if OTP cannot be sent (e.g., invalid data).',
  })
  async generateOtp(@Body() dto: GenerateOtpDto) {
    return this.otpService.generateOtp(dto);
  }

  @Post('verify-otp')
  @ApiOperation({
    summary: 'Verify Email OTP',
    description: 'Verifies the OTP sent to user email',
  })
  @ApiBody({ type: VerifyOtpDto })
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    const user = await this.otpService.verifyOtp(verifyOtpDto);

    // Send verification success notification
    await this.notification.sendSingleNotification(
      user.id,
      'Email Verified',
      'Your email has been successfully verified.',
      { type: 'EMAIL_VERIFIED' },
    );

    if (user.isEmailVerified && user.isPhoneVerified) {
      await this.notification.sendSingleNotification(
        user.id,
        'Account Fully Verified',
        'Your account has been fully verified.',
        { type: 'ACCOUNT_VERIFIED' },
      );
    }

    return this.authService.login(user);
  }

  @Post('sign-up')
  @ApiOperation({
    summary: 'User Sign-Up',
    description: 'Registers a new user with the provided information.',
  })
  @ApiBody({
    type: SignUpDto,
    description: 'Request body containing user registration data.',
  })
  async signUpUser(@Body() dto: SignUpDto) {
    return await this.authService.signUp(dto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Send OTP to email for password reset' })
  @ApiResponse({ status: 200, description: 'OTP sent successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiBody({ type: ForgotPasswordDto })
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.otpService.generateForgotPasswordOtp(forgotPasswordDto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password using OTP' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 401, description: 'Invalid or expired OTP' })
  @ApiBody({ type: ResetPasswordDto })
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.otpService.verifyResetPasswordOtp(resetPasswordDto);
  }

  @Post('update-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update user password',
    description: 'Allows logged-in users to update their password',
  })
  @ApiResponse({
    status: 200,
    description: 'Password updated successfully',
  })
  @ApiBody({ type: UpdatePasswordDto })
  @ApiResponse({
    status: 401,
    description: 'Current password is incorrect',
  })
  async updatePassword(
    @User() user,
    @Body() updatePasswordDto: UpdatePasswordDto,
  ) {
    return this.authService.updatePassword(user.userId, updatePasswordDto);
  }

  @Post('verify-phone')
  @ApiOperation({
    summary: 'Verify phone number',
    description: 'Verifies user phone number using OTP',
  })
  @ApiBody({ type: VerifyPhoneDto })
  async verifyPhone(@Body() verifyPhoneDto: VerifyPhoneDto) {
    const user = await this.otpService.verifyPhoneOtp(verifyPhoneDto);

    await this.notification.sendSingleNotification(
      user.id,
      'Phone Verified',
      'Your phone number has been successfully verified.',
      { type: 'PHONE_VERIFIED' },
    );

    if (user.isEmailVerified && user.isPhoneVerified) {
      await this.notification.sendSingleNotification(
        user.id,
        'Account Fully Verified',
        'Your account has been fully verified.',
        { type: 'ACCOUNT_VERIFIED' },
      );
    }

    return user;
  }

  @Post('generate-phone-otp')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Generate phone verification OTP',
    description: 'Sends OTP to the provided phone number',
  })
  @ApiBody({
    type: UpdatePhoneDto,
  })
  @ApiResponse({
    status: 200,
    description: 'OTP sent successfully',
  })
  async generatePhoneOtp(@Body() dto: GeneratePhoneOtpDto) {
    return this.otpService.generatePhoneOtp(dto);
  }
  @ApiBearerAuth()
  @Patch('update-oauth-details')
  @UseGuards(JwtAuthGuard)
  @ApiBody({ type: UpdateOauthAccount })
  @ApiOperation({ summary: 'Update phone and password for OAuth users' })
  async updateOAuthDetails(
    @User() user,
    @Body() updateDto: UpdateOauthAccount,
  ) {
    return this.authService.updateOAuthDetails(user.userId, updateDto);
  }

  @Post('google/mobile')
  @ApiOperation({
    summary: 'Authenticate via Google',
    description: 'Redirects to Google for OAuth2 authentication.',
  })
  @ApiOkResponse({
    description: 'Returns a message indicating authentication with Google.',
  })
  @ApiBody({ type: GoogleAuthMobileDto })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized if user cannot be authenticated by Google.',
  })
  async googleAuthMobile(@Body() dto: GoogleAuthMobileDto) {
    return await this.authService.googleLoginMobile(dto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('add-phone')
  @ApiOperation({
    summary: 'Add phone number to whitelist',
    description: "Adds a phone number to the user's whitelist",
  })
  @ApiBody({ type: UpdatePhoneDto })
  @ApiResponse({
    status: 200,
    description: 'Phone number added successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  async addPhone(@User() user, @Body() dto: UpdatePhoneDto) {
    return this.phonesService.addPhone(user.userId, dto.phone);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-phone/:id')
  @ApiOperation({
    summary: 'Remove phone number from whitelist',
    description: "Removes a phone number from the user's whitelist",
  })
  @ApiResponse({
    status: 200,
    description: 'Phone number removed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  async removePhone(@User() user, @Param('id') phoneId: string) {
    return this.phonesService.removePhone(user.userId, phoneId);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('phones')
  @ApiOperation({
    summary: 'Get user phones',
    description: "Gets all phone numbers in the user's whitelist",
  })
  @ApiResponse({
    status: 200,
    description: 'Phone numbers retrieved successfully',
  })
  async getUserPhones(@User() user) {
    return this.phonesService.getUserPhones(user.userId);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('white-list-phone')
  @ApiOperation({
    summary: 'White list phone number',
    description: 'Adds phone number to the user whitelist',
  })
  @ApiBody({
    type: UpdatePhoneDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Phone number added to whitelist successfully',
  })
  async whiteListPhone(@User() user, @Body() { phone }: UpdatePhoneDto) {
    return this.phonesService.generatePhoneOtp(user.userId, phone);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('verify-whitelisting')
  @ApiOperation({
    summary: 'Verify phone number',
    description: 'Verifies user phone number using OTP',
  })
  @ApiBody({ type: VerifyPhoneDto })
  @ApiResponse({
    status: 200,
    description: 'Phone number verified successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  async verifyWhiteListing(
    @User() user,
    @Body() verifyPhoneDto: VerifyPhoneDto,
  ) {
    return this.phonesService.verifyPhone(
      user.userId,
      verifyPhoneDto.phone,
      verifyPhoneDto.otpCode,
    );
  }
}
