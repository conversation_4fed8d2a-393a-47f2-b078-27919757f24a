import { Prisma } from '@prisma/client';

export const UsersSelect = {
  id: true,
  email: true,
  fullname: true,
  googleId: true,
  role: true,
  phone: true,
  isVerified: true,
  createdAt: true,
  updatedAt: true,
  address: true,
  profile: true,
  orders: true,
  driverDetails: true,
  adminDetails: true,
  otps: true,
};
export type UserEntityPayload = Prisma.UserGetPayload<{
  select: {
    id: true;
    email: true;
    fullname: true;
    googleId: true;
    role: true;
    phone: true;
    isVerified: true;
    createdAt: true;
    updatedAt: true;
    address: true;
    profile: true;
    orders: true;
    driverDetails: true;
    adminDetails: true;
    otps: true;
  };
}>;
