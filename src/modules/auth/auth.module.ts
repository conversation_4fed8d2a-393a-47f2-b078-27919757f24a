import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { LocalStrategy } from 'src/common/strategies/local.strategy';
import { GoogleStrategy } from 'src/common/strategies/google.strategy';
import { JwtStrategy } from 'src/common/strategies/jwt.strategy';
import { UsersModule } from '../users/users.module';
import { EmailService } from 'src/core/email/email.service';
import { OtpService } from './opt.service';
import { NotificationModule } from '../notification/notification.module';
import { TwilioService } from 'src/core/sms/twilio.service';
import { PhonesService } from './phones.service';
import {
  OtpRepository,
  UsersRepository,
  ProfileRepository,
} from 'src/common/repositories';
import { PaymentAccountsRepository } from 'src/common/repositories/payment-accounts.repository';

@Module({
  imports: [
    PassportModule,
    UsersModule,
    NotificationModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION', '7d'),
        },
      }),
    }),
  ],
  providers: [
    AuthService,
    JwtStrategy,
    LocalStrategy,
    GoogleStrategy,
    EmailService,
    UsersRepository,
    ProfileRepository,
    PaymentAccountsRepository,
    OtpRepository,
    OtpService,
    TwilioService,
    PhonesService,
  ],
  exports: [AuthService],
  controllers: [AuthController],
})
export class AuthModule {}
