import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { GoogleAuthDto } from './dto/google-auth.dto';
import * as bcrypt from 'bcrypt';
import { LocalLoginDto } from './dto/local-login.dto';
import { SignUpDto } from './dto/sign-up.dto';

import { UserRole } from '@prisma/client';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { NotificationService } from '../notification/notification.service';
import { GoogleAuthMobileDto } from './dto/google-auth-mobile.dto';
import { EmailService } from 'src/core/email/email.service';
import { UsersRepository } from 'src/common/repositories';
import { Users } from 'src/common/schemas';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    // private readonly prisma: PrismaService,
    private readonly usersRepository: UsersRepository,
    private readonly notificationService: NotificationService,
    private readonly emailService: EmailService,
  ) {}

  async localLogin(dto: LocalLoginDto) {
    const user = await this.usersRepository.getUserByIdentifier(dto.identifier);

    if (!user) throw new BadRequestException('Wrong credentials');

    if (user && user.isDeleted) {
      throw new BadRequestException('Account not found');
    }

    if (!user.password) {
      throw new BadRequestException(
        'Consider login in with Google or add a Password to your account',
      );
    }

    const isPasswordValid = await bcrypt.compare(dto.password, user.password);

    if (user && isPasswordValid) {
      const token = await this.login(user);
      this.notificationService.sendSingleNotification(
        user.id,
        'Login Success',
        'You have successfully logged in with email and password',
        { type: 'LOGIN_SUCCESS' },
      );
      return {
        user,
        ...token,
      };
    } else {
      throw new BadRequestException('Wrong credentials');
    }
  }

  async googleLogin(dto: GoogleAuthDto) {
    let user = await this.usersRepository.getUserByEmail(dto.email);

    if (user && user.isDeleted) {
      throw new BadRequestException(
        'Account was deleted. Please contact support',
      );
    }
    if (!user) {
      user = await this.usersRepository.addGoogleUser({
        email: dto.email,
        fullname: dto.fullname,
        googleId: dto.id,
        isEmailVerified: dto.verified,
        avatar: dto.avatar,
      });
    }

    return this.login(user);
  }

  async login(user: Partial<Users>) {
    const payload = {
      email: user.email,
      sub: user.id,
      phone: user.phone,
      verified: user.isVerified,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      role: user.role,
    };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }

  async signUp(dto: SignUpDto) {
    let user = await this.usersRepository.getUserByEmail(dto.email);

    // const phoneUser = await this.prisma.user.findUnique({
    //   where: { phone: dto.phone },
    // });

    if (user && user.isDeleted) {
      throw new BadRequestException(
        'Account was deleted. Please contact support',
      );
    }
    // if (phoneUser) throw new BadRequestException('Phone Number Already taken');
    if (user) throw new BadRequestException('Email Already taken');

    //hash password
    const hashedPassword = await bcrypt.hash(dto.password, 10);

    if (!user) {
      user = await this.usersRepository.create({
        email: dto.email,
        fullname: dto.fullname,
        password: hashedPassword,
        // phone: dto.phone,
        role: UserRole.CUSTOMER,
      });

      await this.usersRepository.update(user.id, {
        profile: {
          create: {
            gender: null,
            avatar: 'https://avatar.iran.liara.run/public/50',
            nickname: null,
          },
        },
      });

      this.notificationService.sendSingleNotification(
        user.id,
        'Account Created',
        'Your account has been successfully created.',
        { type: 'ACCOUNT_CREATED' },
      );

      await this.emailService.sendWelcomeEmail(user.email, user.fullname);

      return user;
    }
  }

  async updateFirebaseToken(userId: string, fcmToken: string) {
    return this.usersRepository.updateFirebaseToken(userId, {
      fcmToken: fcmToken,
    });
  }

  async updatePassword(userId: string, updatePasswordDto: UpdatePasswordDto) {
    const user = await this.usersRepository.getUserWithPassword(userId);

    if (!user) throw new BadRequestException('User not found');

    if (
      !(await bcrypt.compare(updatePasswordDto.currentPassword, user.password))
    )
      throw new BadRequestException('Current password is incorrect');

    const hashedPassword = await bcrypt.hash(updatePasswordDto.newPassword, 10);

    return this.usersRepository.update(userId, { password: hashedPassword });
  }

  async updateOAuthDetails(
    userId: string,
    updateDto: { phone?: string; password?: string },
  ) {
    const user = await this.usersRepository.findOne(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updates: any = {};

    if (updateDto.phone) {
      updates.phone = updateDto.phone;
    }

    if (updateDto.password) {
      updates.password = await bcrypt.hash(updateDto.password, 10);
    }

    return this.usersRepository.update(userId, updates);
  }

  async googleLoginMobile(dto: GoogleAuthMobileDto) {
    const existingUser = await this.usersRepository.getUserByEmail(dto.email);

    if (existingUser && existingUser.isDeleted) {
      throw new BadRequestException(
        'Account was deleted. Please contact support',
      );
    }

    let user = await this.usersRepository.getUserByEmail(dto.email);

    if (!user) {
      user = await this.usersRepository.addGoogleUser({
        email: dto.email,
        fullname: dto.fullname,
        googleId: dto.googleId,
        isEmailVerified: dto.verified,
        avatar: dto.avatar,
      });
      this.notificationService.sendSingleNotification(
        user.id,
        'Account Created',
        'Your account has been successfully created.',
        { type: 'ACCOUNT_CREATED' },
      );
    }

    return this.login(user);
  }
}
