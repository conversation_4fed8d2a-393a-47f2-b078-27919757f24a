import {
  IsEmail,
  <PERSON>String,
  IsO<PERSON>al,
  IsPhoneNumber,
  IsUrl,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { UserRole } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty()
  @IsOptional()
  @IsEmail()
  email?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  fullName?: string;
  @ApiProperty()
  @IsOptional()
  @IsPhoneNumber()
  phoneNumber?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  password?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  googleId?: string;
  @ApiProperty()
  @IsOptional()
  @IsUrl()
  profileImage?: string;
  @ApiProperty()
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  verified?: boolean;
}
