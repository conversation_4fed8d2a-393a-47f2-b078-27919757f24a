import { IsEmail, IsString, IsNotEmpty, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GoogleAuthMobileDto {
  @ApiProperty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  googleId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fullname: string;

  @ApiProperty()
  @IsBoolean()
  @IsNotEmpty()
  verified: boolean;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  avatar: string;
}
