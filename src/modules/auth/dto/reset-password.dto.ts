import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty({
    description: 'OTP token for password reset',
    example: 'd1e87a9d-12d3-4b56-8c9d-0e0f1a234567',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'New password for the user',
    example: 'newpassword123',
  })
  @IsString()
  @IsNotEmpty()
  newPassword: string;
}
