import { ApiProperty } from '@nestjs/swagger';
import { IsString, Min<PERSON>ength, IsNotEmpty } from 'class-validator';

export class UpdatePasswordDto {
  @ApiProperty({
    description: 'Current password',
    example: 'currentPassword123'
  })
  @IsString()
  @IsNotEmpty()
  currentPassword: string;

  @ApiProperty({
    description: 'New password',
    example: 'newPassword123'
  })
  @IsString()
  @MinLength(8)
  @IsNotEmpty()
  newPassword: string;
}