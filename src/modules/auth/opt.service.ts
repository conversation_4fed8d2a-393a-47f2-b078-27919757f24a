import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { EmailService } from 'src/core/email/email.service';
import { GenerateOtpDto } from './dto/generate-otp.dto';
import * as bcrypt from 'bcrypt';
import { VerifyOtpDto } from './dto/verify-otp';
import { OTPType } from '@prisma/client';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { VerifyPhoneDto } from './dto/verify-phone.dto';
import { GeneratePhoneOtpDto } from './dto/generate-phone-otp.dto';
import { TwilioService } from 'src/core/sms/twilio.service';
import { ConfigService } from '@nestjs/config';
import { OtpRepository, UsersRepository } from 'src/common/repositories';

@Injectable()
export class OtpService {
  constructor(
    private readonly emailService: EmailService,
    private readonly twilioService: TwilioService,
    private readonly usersRepository: UsersRepository,
    private readonly otpRepository: OtpRepository,
    private readonly configService: ConfigService,
  ) {}

  async generateOtp(dto: GenerateOtpDto): Promise<string> {
    try {
      const user = await this.usersRepository.getUserByEmail(dto.email);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);

      await this.otpRepository.create({
        userId: user.id,
        code: otp,
        type: OTPType.EMAIL_VERIFICATION,
        expiresAt: otpExpiry,
        isUsed: false,
      });

      await this.emailService.sendUserOtp(
        dto.email,
        user.fullname,
        otp,
        'Your One-Time Password (OTP) for Verification',
        'We received a request to verify your account. Please use the One-Time Password (OTP) below to complete the verification process',
      );
      return 'OTP sent successfully';
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async verifyOtp(dto: VerifyOtpDto) {
    const otpRecord = await this.otpRepository.findOne(dto.otpCode);

    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (otpRecord.isUsed) {
      throw new UnauthorizedException('OTP already used');
    }

    if (new Date() > otpRecord.expiresAt) {
      throw new UnauthorizedException('OTP has expired');
    }

    await this.otpRepository.remove(otpRecord.id);

    // const updatedUser = await this.prisma.user.update({
    //   where: { email: dto.email },
    //   data: {
    //     isEmailVerified:
    //       dto.otpType === OTPType.EMAIL_VERIFICATION ? true : undefined,
    //   },
    // });

    const updatedUser = await this.usersRepository.update(otpRecord.user.id, {
      isEmailVerified: true,
    });

    // Check if both email and phone are verified
    if (updatedUser.isEmailVerified && updatedUser.isPhoneVerified) {
      await this.usersRepository.update(otpRecord.user.id, {
        isVerified: true,
      });
    }

    return updatedUser;
  }

  async generateForgotPasswordOtp(dto: ForgotPasswordDto): Promise<string> {
    const user = await this.usersRepository.getUserByEmail(dto.email);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const token = crypto.randomUUID();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);

    await this.otpRepository.create({
      userId: user.id,
      code: token,
      type: OTPType.PASSWORD_RESET,
      expiresAt: otpExpiry,
      isUsed: false,
    });

    await this.emailService.sendUserOtp(
      user.email,
      user.fullname,
      token,
      'Hello Your OTP for Password Reset',
      'To reset your password, please use the One-Time Password (OTP) below.',
    );
    return 'OTP for password reset sent successfully';
  }

  async verifyResetPasswordOtp(dto: ResetPasswordDto): Promise<string> {
    const otpRecord = await this.otpRepository.findOne(dto.token);
    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (otpRecord.isUsed) {
      throw new UnauthorizedException('OTP already used');
    }

    if (new Date() > otpRecord.expiresAt) {
      throw new UnauthorizedException('OTP has expired');
    }

    await this.usersRepository.update(otpRecord.user.id, {
      password: await bcrypt.hash(dto.newPassword, 10),
    });
    await this.otpRepository.remove(otpRecord.id);

    return 'Password has been reset successfully';
  }

  async generatePhoneOtp(dto: GeneratePhoneOtpDto): Promise<string> {
    try {
      const user = await this.usersRepository.getUserByPhone(dto.phone);

      if (!user) throw new NotFoundException('User not found');

      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const otpExpiry = new Date(Date.now() + 10 * 60 * 1000);

      await this.otpRepository.create({
        userId: user.id,
        code: otp,
        type: OTPType.PHONE_VERIFICATION,
        expiresAt: otpExpiry,
        isUsed: false,
      });

      await this.twilioService.sendSms(
        dto.phone,
        `Your verification code for ${this.configService.get('APP_NAME')} is: ${otp}\n\n` +
          'This code will expire in 10 minutes.\n' +
          'For security reasons, please do not share this code with anyone.\n\n' +
          'If you did not request this code, please ignore this message.',
      );

      return 'OTP sent successfully';
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async verifyPhoneOtp(dto: VerifyPhoneDto) {
    const otpRecord = await this.otpRepository.findOne(dto.otpCode);

    if (!otpRecord) {
      throw new UnauthorizedException('Invalid OTP');
    }

    if (otpRecord.isUsed) {
      throw new UnauthorizedException('OTP already used');
    }

    if (new Date() > otpRecord.expiresAt) {
      throw new UnauthorizedException('OTP has expired');
    }

    await this.otpRepository.remove(otpRecord.id);

    const updatedUser = await this.usersRepository.update(otpRecord.user.id, {
      isPhoneVerified: true,
      phone: dto.phone,
      isVerified: true,
    });

    // Check if both email and phone are verified
    if (updatedUser.isEmailVerified && updatedUser.isPhoneVerified) {
      await this.usersRepository.update(otpRecord.user.id, {
        isVerified: true,
      });
    }

    return updatedUser;
  }
}
