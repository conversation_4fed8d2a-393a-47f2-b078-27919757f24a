import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { generateOtp } from 'src/common/utils/otp';
import { TwilioService } from 'src/core/sms/twilio.service';
import { OTPType } from '@prisma/client';
import {
  OtpRepository,
  UsersRepository,
  PaymentAccountsRepository,
  ProfileRepository,
} from 'src/common/repositories';

@Injectable()
export class PhonesService {
  constructor(
    private readonly twilioService: TwilioService,
    private readonly paymentAccountsRepository: PaymentAccountsRepository,
    private readonly otpRepository: OtpRepository,
    private readonly usersRepository: UsersRepository,
    private readonly profileRepository: ProfileRepository,
  ) {}

  async addPhone(userId: string, phone: string) {
    try {
      // Check if user exists
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new NotFoundException('User not found');

      // Get user's profile
      const profile = await this.profileRepository.findOne(userId);
      if (!profile) throw new NotFoundException('User profile not found');

      // Check if phone already exists in whitelist
      const existingPhone =
        await this.paymentAccountsRepository.findByNumber(phone);

      if (existingPhone) {
        throw new BadRequestException(
          'Phone number already exists in whitelist',
        );
      }

      // Check if user already has 3 whitelisted numbers
      const userPhones =
        await this.paymentAccountsRepository.findByUserId(userId);

      if (userPhones.length >= 3) {
        throw new BadRequestException(
          'Maximum of 3 whitelisted numbers allowed',
        );
      }

      // Add phone to whitelist using the profile ID
      const newPhone = await this.paymentAccountsRepository.create({
        number: phone,
        profile: { connect: { id: profile.id } },
      });

      // send otp via sms
      await this.whiteListNumber(newPhone.number, userId);

      return newPhone;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async removePhone(userId: string, phoneId: string) {
    try {
      // Check if phone exists and belongs to user
      const existingPhone =
        await this.paymentAccountsRepository.findOne(phoneId);
      if (existingPhone.profile.user.id !== userId) {
        throw new BadRequestException(
          'Phone number not found or does not belong to user',
        );
      }

      if (!existingPhone) {
        throw new NotFoundException(
          'Phone number not found or does not belong to user',
        );
      }

      await this.paymentAccountsRepository.remove(phoneId);

      return { message: 'Phone number removed successfully' };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getUserPhones(userId: string) {
    try {
      const phones = await this.paymentAccountsRepository.findByUserId(userId);

      return phones;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async generatePhoneOtp(userId: string, phone: string) {
    try {
      const number = await this.paymentAccountsRepository.findByNumber(phone);

      if (!number) {
        throw new NotFoundException(
          'Phone number not found or does not belong to user',
        );
      }

      if (number.isVerified) {
        throw new BadRequestException('Phone number already verified');
      }

      // Generate OTP

      const { otp, otpExpiry } = generateOtp();

      await this.otpRepository.create({
        code: otp,
        userId,
        expiresAt: otpExpiry,
        isUsed: false,
        type: OTPType.PHONE_VERIFICATION,
      });

      await this.twilioService.sendSms(
        number.number,
        `Welcome to Zakaz!\n\n` +
          `Your verification code is: ${otp}\n\n` +
          `This code will expire in 10 minutes.\n` +
          `Please do not share this code with anyone.`,
      );

      // Send OTP via SMS (assuming optService handles this)

      return { message: 'OTP sent successfully' };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async verifyPhone(userId: string, phone: string, otpCode: string) {
    try {
      const number = await this.paymentAccountsRepository.findByNumber(phone);

      if (!number)
        throw new NotFoundException(
          'Phone number not found or does not belong to user',
        );
      if (number.isVerified) {
        throw new BadRequestException('Phone number already verified');
      }

      await this.verifyWhiteListNumber(otpCode);
      const updatedPhone = await this.paymentAccountsRepository.update(
        number.id,
        { isVerified: true },
      );
      return updatedPhone;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  private async whiteListNumber(number: string, userId: string) {
    // generate otp
    const { otp, otpExpiry } = generateOtp();

    // save otp to db
    await this.otpRepository.create({
      code: otp,
      userId,
      expiresAt: otpExpiry,
      isUsed: false,
      type: OTPType.PHONE_VERIFICATION,
    });

    // send otp via sms
    await this.twilioService.sendSms(
      number,
      `Welcome to Zakaz!\n\n` +
        `Your verification code is: ${otp}\n\n` +
        `This code will expire in 10 minutes.\n` +
        `Please do not share this code with anyone.`,
    );
  }

  private async verifyWhiteListNumber(otpCode: string) {
    const otpRecord = await this.otpRepository.findOne(otpCode);

    if (!otpRecord) {
      throw new BadRequestException('Invalid OTP');
    }

    if (otpRecord.isUsed) {
      throw new BadRequestException('OTP already used');
    }

    if (new Date() > otpRecord.expiresAt) {
      throw new BadRequestException('OTP has expired');
    }

    await this.otpRepository.remove(otpRecord.id);
  }

  private async removeWhiteListNumber(id: string) {
    await this.paymentAccountsRepository.remove(id);
  }
}
