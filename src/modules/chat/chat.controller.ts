import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Patch,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { CreateChatDto } from './dto/create-chat.dto';
import { CreateMessageDto } from './dto/create-message.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { ChatStatus } from '@prisma/client';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { IdValidationPipe } from 'src/common/pipes/id-validation.pipe';

@ApiTags('chat')
@Controller('chat')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new chat' })
  async createChat(@User() user, @Body() createChatDto: CreateChatDto) {
    return this.chatService.createChat(user.userId, createChatDto);
  }

  @Post(':chatId/messages')
  @ApiOperation({ summary: 'Add a message to a chat' })
  async addMessage(
    @User() user,
    @Param('chatId', new IdValidationPipe()) chatId: string,
    @Body() createMessageDto: CreateMessageDto,
  ) {
    return this.chatService.addMessage(chatId, user.userId, createMessageDto);
  }

  @Get('my-chats')
  @ApiOperation({ summary: "Get user's chats" })
  async getUserChats(@User() user) {
    return this.chatService.getUserChats(user.userId);
  }

  @Get('admin')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Get all chats (admin only)' })
  async getAdminChats() {
    return this.chatService.getAdminChats();
  }

  @Get(':chatId')
  @ApiOperation({ summary: 'Get chat messages' })
  async getChatMessages(
    @User() user,
    @Param('chatId', new IdValidationPipe()) chatId: string,
  ) {
    return this.chatService.getChatMessages(chatId, user.userId);
  }

  @Patch(':chatId/status')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Update chat status (admin only)' })
  async updateChatStatus(
    @Param('chatId', new IdValidationPipe()) chatId: string,
    @Body('status') status: ChatStatus,
  ) {
    return this.chatService.updateChatStatus(chatId, status);
  }
}
