import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { WebsocketGuard } from 'src/common/guards/ws-gaurd.guard';
import { SocketAuthMiddleware } from 'src/common/middleware/ws.middleware';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { UserRole, AdminPermission } from '@prisma/client';

interface MessageDto {
  content: string;
  chatId: string;
}

@UseGuards(WebsocketGuard)
@WebSocketGateway({
  namespace: 'chat-gateway',
  cors: {
    origin: ['*'],
  },
})
export class ChatGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  private readonly logger = new Logger(ChatGateway.name);
  private chatAdminSockets = new Set<string>();
  private userSocketMap = new Map<
    string,
    {
      userId: string;
      role: UserRole;
      permissions?: AdminPermission[];
    }
  >();

  @WebSocketServer()
  server: Server;

  constructor(private prisma: PrismaService) {}

  afterInit(server: Server) {
    server.use(SocketAuthMiddleware());
  }

  async handleConnection(client: Socket) {
    const user = await this.prisma.user.findUnique({
      where: { id: client.data.user.sub },
      include: {
        adminDetails: true,
      },
    });

    if (!user) {
      client.disconnect();
      return;
    }

    // Store user info with permissions if admin
    this.userSocketMap.set(client.id, {
      userId: user.id,
      role: user.role as UserRole,
      permissions: user.adminDetails?.permissions,
    });

    // If admin with MANAGE_CHAT permission, add to chat admin sockets
    if (
      user.role === UserRole.ADMIN &&
      user.adminDetails?.permissions.includes(AdminPermission.MANAGE_CHAT)
    ) {
      this.chatAdminSockets.add(client.id);
      client.join('chat-admin-room');
    }

    client.emit('connected', {
      userId: user.id,
      role: user.role,
      permissions: user.adminDetails?.permissions || [],
    });
  }

  handleDisconnect(client: Socket) {
    this.chatAdminSockets.delete(client.id);
    this.userSocketMap.delete(client.id);
  }

  private hasManageChatPermission(socketId: string): boolean {
    const userInfo = this.userSocketMap.get(socketId);
    return (
      userInfo?.role === UserRole.ADMIN &&
      userInfo?.permissions?.includes(AdminPermission.MANAGE_CHAT)
    );
  }

  @SubscribeMessage('send_message')
  async handleMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: MessageDto,
  ) {
    const userInfo = this.userSocketMap.get(client.id);

    if (!userInfo) {
      client.emit('error', { message: 'Unauthorized' });
      return { success: false };
    }

    // Only allow customers to send messages
    if (userInfo.role !== UserRole.CUSTOMER) {
      client.emit('error', { message: 'Only customers can send messages' });
      return { success: false };
    }

    const messageData = {
      userId: userInfo.userId,
      message: payload.content,
      timestamp: new Date(),
      type: 'customer_message',
    };

    try {
      // Store message in database
      await this.prisma.message.create({
        data: {
          senderId: userInfo.userId,
          content: payload.content,
          chatId: payload.chatId,
        },
      });

      // Broadcast to all admins with MANAGE_CHAT permission
      this.server.to('chat-admin-room').emit('customer_message', {
        ...messageData,
        userName: (
          await this.prisma.user.findUnique({
            where: { id: userInfo.userId },
            select: { fullname: true },
          })
        )?.fullname,
      });

      client.emit('message_sent', { success: true });
      return { success: true };
    } catch (error) {
      this.logger.error('Error handling message:', error);
      client.emit('error', { message: 'Failed to process message' });
      return { success: false };
    }
  }

  @SubscribeMessage('admin_reply')
  async handleAdminReply(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: MessageDto,
  ) {
    if (!this.hasManageChatPermission(client.id)) {
      client.emit('error', {
        message: 'Unauthorized. Requires MANAGE_CHAT permission',
      });
      return { success: false };
    }

    const adminInfo = this.userSocketMap.get(client.id);

    try {
      // Store admin reply
      await this.prisma.message.create({
        data: {
          senderId: adminInfo.userId,
          content: payload.content,
          chatId: payload.chatId,
        },
      });

      const replyData = {
        adminId: adminInfo.userId,
        message: payload.content,
        timestamp: new Date(),
        type: 'admin_reply',
      };

      const chatInfo = await this.prisma.chat.findUnique({
        where: { id: payload.chatId },
        include: {
          user: true,
        },
      });

      if (!chatInfo) {
        client.emit('error', { message: 'Chat not found' });
        return { success: false };
      }

      // Send to specific user if they're online
      const userSocket = Array.from(this.userSocketMap.entries()).find(
        ([_, info]) => info.userId === chatInfo.user.id,
      )?.[0];

      if (userSocket) {
        this.server.to(userSocket).emit('admin_reply', replyData);
      }

      // Broadcast to other chat admins
      this.server.to('chat-admin-room').emit('admin_reply_sent', {
        ...replyData,
        userId: chatInfo.user.id,
      });

      return { success: true };
    } catch (error) {
      this.logger.error('Error handling admin reply:', error);
      client.emit('error', { message: 'Failed to send reply' });
      return { success: false };
    }
  }

  @SubscribeMessage('get_online_chat_admins')
  getOnlineChatAdmins() {
    return { count: this.chatAdminSockets.size };
  }

  @SubscribeMessage('start_typing')
  async handleStartTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: { chatId: string },
  ) {
    const userInfo = this.userSocketMap.get(client.id);

    if (!userInfo) {
      return { success: false };
    }

    const typingData = {
      userId: userInfo.userId,
      chatId: payload.chatId,
      userRole: userInfo.role,
      timestamp: new Date(),
    };

    if (userInfo.role === UserRole.CUSTOMER) {
      // Notify admins that customer is typing
      this.server.to('chat-admin-room').emit('customer_typing', typingData);
    } else if (this.hasManageChatPermission(client.id)) {
      // Notify customer that admin is typing
      const chatInfo = await this.prisma.chat.findUnique({
        where: { id: payload.chatId },
        include: { user: true },
      });

      if (chatInfo) {
        const userSocket = Array.from(this.userSocketMap.entries()).find(
          ([_, info]) => info.userId === chatInfo.user.id,
        )?.[0];

        if (userSocket) {
          this.server.to(userSocket).emit('admin_typing', typingData);
        }
      }
    }

    return { success: true };
  }

  @SubscribeMessage('stop_typing')
  async handleStopTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() payload: { chatId: string },
  ) {
    const userInfo = this.userSocketMap.get(client.id);

    if (!userInfo) {
      return { success: false };
    }

    const typingData = {
      userId: userInfo.userId,
      chatId: payload.chatId,
      userRole: userInfo.role,
      timestamp: new Date(),
    };

    if (userInfo.role === UserRole.CUSTOMER) {
      this.server
        .to('chat-admin-room')
        .emit('customer_stopped_typing', typingData);
    } else if (this.hasManageChatPermission(client.id)) {
      const chatInfo = await this.prisma.chat.findUnique({
        where: { id: payload.chatId },
        include: { user: true },
      });

      if (chatInfo) {
        const userSocket = Array.from(this.userSocketMap.entries()).find(
          ([_, info]) => info.userId === chatInfo.user.id,
        )?.[0];

        if (userSocket) {
          this.server.to(userSocket).emit('admin_stopped_typing', typingData);
        }
      }
    }

    return { success: true };
  }
}
