import { Modu<PERSON> } from '@nestjs/common';
import { ChatService } from './chat.service';
import { ChatController } from './chat.controller';
// import { PrismaService } from 'src/core/prisma/prisma.service';
import { NotificationModule } from '../notification/notification.module';
import { ChatGateway } from './chat.gateway';

@Module({
  imports: [NotificationModule, ChatModule],
  controllers: [ChatController],
  providers: [ChatService, ChatGateway],
})
export class ChatModule {}
