import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { CreateChatDto } from './dto/create-chat.dto';
import { CreateMessageDto } from './dto/create-message.dto';
import { ChatStatus } from '@prisma/client';
import { NotificationService } from '../notification/notification.service';

@Injectable()
export class ChatService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
  ) {}

  async createChat(userId: string, createChatDto: CreateChatDto) {
    const chat = await this.prisma.chat.create({
      data: {
        userId,
        title: createChatDto.title,
        messages: {
          create: {
            content: createChatDto.initialMessage,
            senderId: userId,
          },
        },
      },
      include: {
        messages: true,
        user: true,
      },
    });

    // Notify admins about new chat
    const admins = await this.prisma.user.findMany({
      where: { role: 'ADMIN' },
    });

    await this.notificationService.sendBulkNotifications(
      admins.map((admin) => admin.id),
      'New Support Chat',
      `New support request: ${createChatDto.title}`,
      {
        type: 'NEW_CHAT',
        chatId: chat.id,
      },
    );

    return chat;
  }

  async addMessage(
    chatId: string,
    userId: string,
    createMessageDto: CreateMessageDto,
  ) {
    const chat = await this.prisma.chat.findUnique({
      where: { id: chatId },
      include: { user: true },
    });

    if (!chat) {
      throw new NotFoundException('Chat not found');
    }

    const message = await this.prisma.message.create({
      data: {
        chatId,
        senderId: userId,
        content: createMessageDto.content,
      },
      include: {
        sender: true,
      },
    });

    // Notify the other party
    const recipientId =
      userId === chat.userId
        ? (await this.prisma.user.findFirst({ where: { role: 'ADMIN' } }))?.id
        : chat.userId;

    if (recipientId) {
      await this.notificationService.sendSingleNotification(
        recipientId,
        'New Message',
        `New message in chat: ${chat.title}`,
        {
          type: 'NEW_MESSAGE',
          chatId: chat.id,
        },
      );
    }

    // Send real-time event
    // this.eventsRealService.broadcastToRoom(`chat:${chatId}`, 'new_message', {
    //   chatId,
    //   message,
    // });

    return message;
  }

  async getUserChats(userId: string) {
    return this.prisma.chat.findMany({
      where: { userId },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async getAdminChats() {
    return this.prisma.chat.findMany({
      include: {
        user: true,
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
  }

  async getChatMessages(chatId: string, userId: string) {
    const chat = await this.prisma.chat.findUnique({
      where: { id: chatId },
      include: {
        messages: {
          include: {
            sender: true,
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!chat) {
      throw new NotFoundException('Chat not found');
    }

    // Mark messages as read
    await this.prisma.message.updateMany({
      where: {
        chatId,
        senderId: { not: userId },
        isRead: false,
      },
      data: { isRead: true },
    });

    return chat;
  }

  async updateChatStatus(chatId: string, status: ChatStatus) {
    const chat = await this.prisma.chat.update({
      where: { id: chatId },
      data: { status },
      include: { user: true },
    });

    await this.notificationService.sendSingleNotification(
      chat.userId,
      'Chat Status Updated',
      `Your chat "${chat.title}" has been marked as ${status.toLowerCase()}`,
      {
        type: 'CHAT_STATUS_UPDATE',
        chatId: chat.id,
        status,
      },
    );

    return chat;
  }
}
