import {
  Controller,
  Get,
  Param,
  Patch,
  Body,
  UseGuards,
  Query,
  Post,
} from '@nestjs/common';

import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { ErrandStatus } from '@prisma/client';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { DeliveriesService } from './deliveries.service';
import { IsDriverGuard } from 'src/common/guards/isDriver.guard';
import { User } from 'src/common/decorators/user.decorator';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { AcceptDeliveryDto } from './dto/accept-delivery.dto';
import { UpdateDeliveryLocationDto } from './dto/update-deliver-location.dto';
import { IsCustomerGuard } from 'src/common/guards/isCustomer.guard';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { AssignDelivery } from './dto/assign.dto';

@ApiTags('deliveries')
@Controller('deliveries')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeliveriesController {
  constructor(private readonly deliveriesService: DeliveriesService) {}

  @Post('assign')
  async assignDelivery(@Body() dto: AssignDelivery) {
    return this.deliveriesService.assignDriver(dto);
  }
  @Get()
  @UseGuards(IsAdminGuard)
  @ApiQuery({ type: PaginationDto })
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all deliveries' })
  async getAllDeliveries(@Query() pagination: PaginationDto) {
    return this.deliveriesService.getAllDeliveries(pagination);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get delivery by ID' })
  findOne(@Param('id') id: string) {
    return this.deliveriesService.getDeliveryById(id);
  }
  @Get(':driverId/driver')
  @ApiQuery({ type: PaginationDto })
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get deliveries for a driver' })
  getDriverDeliveries(
    @Query() pagination: PaginationDto,
    @Param('driverId') driverId: string,
  ) {
    return this.deliveriesService.getDriverDeliveries(
      driverId,
      pagination,
      undefined,
    );
  }

  @Get('driver/status/:status')
  @ApiQuery({ type: PaginationDto })
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get driver deliveries by status' })
  getDriverDeliveriesByStatus(
    @Param('status') status: ErrandStatus,
    @Query() pagination: PaginationDto,
    @User() user,
  ) {
    return this.deliveriesService.getDriverDeliveries(
      user.userId,
      pagination,
      status,
    );
  }

  @Patch(':id/status')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update delivery status' })
  updateStatus(@Param('id') id: string, @Query('status') status: ErrandStatus) {
    return this.deliveriesService.updateDeliveryStatus(id, status);
  }

  @Patch(':id/accept')
  @UseGuards(IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Driver accepts a delivery' })
  async acceptDelivery(
    @Param('id') deliveryId: string,
    @User() user,
    @Body() body: AcceptDeliveryDto,
  ) {
    return this.deliveriesService.acceptDelivery(deliveryId, user.userId, {
      latitude: body.latitude,
      longitude: body.longitude,
    });
  }

  @Get('driver/my-deliveries')
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current driver's deliveries" })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ErrandStatus,
    description: 'Filter by delivery status',
  })
  @ApiQuery({ type: PaginationDto })
  @ApiResponse({
    status: 200,
    description: "Returns driver's deliveries with details",
  })
  async getMyDeliveries(
    @User() user,
    @Query() pagination: PaginationDto,
    @Query('status') status?: ErrandStatus,
  ) {
    return this.deliveriesService.getDriverDeliveries(
      user.userId,
      pagination,
      status,
    );
  }

  @Get('driver/my-deliveries/today')
  @UseGuards(IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Get current driver's deliveries for today" })
  @ApiQuery({ type: PaginationDto })
  @ApiResponse({
    status: 200,
    description: "Returns driver's deliveries for today with statistics",
  })
  async getMyTodayDeliveries(@User() user, @Query() pagination: PaginationDto) {
    return this.deliveriesService.getDriverDailyDeliveries(
      user.userId,
      pagination,
    );
  }

  @Patch(':id/location')
  @UseGuards(IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update delivery location' })
  async updateDeliveryLocation(
    @Param('id') deliveryId: string,
    @User() user,
    @Body() body: UpdateDeliveryLocationDto,
  ) {
    return this.deliveriesService.updateDeliveryLocation({
      deliveryId,
      driverId: user.userId,
      location: body,
    });
  }
  @Get(':id/location')
  @UseGuards(IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Track delivery location' })
  async trackDeliveryLocation(@Param('id') deliveryId: string, @User() user) {
    return this.deliveriesService.trackDeliveryLocation({
      deliveryId,
      driverId: user.userId,
    });
  }

  @Patch(':id/start')
  @UseGuards(IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Start a delivery' })
  async startDelivery(@Param('id') deliveryId: string, @User() user) {
    return this.deliveriesService.startDelivery(user.userId, deliveryId);
  }

  @Patch(':id/end')
  @ApiBearerAuth()
  @UseGuards(IsCustomerGuard)
  @ApiOperation({ summary: 'End a delivery' })
  async endDelivery(@Param('id') deliveryId: string, @User() user) {
    return this.deliveriesService.endDelivery({
      deliveryId,
      customerId: user.userId,
    });
  }
}
