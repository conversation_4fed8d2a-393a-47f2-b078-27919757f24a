import { Module } from '@nestjs/common';
import { DeliveriesService } from './deliveries.service';
import { DeliveriesController } from './deliveries.controller';
import { NotificationService } from '../notification/notification.service';
import {
  DeliveryRepository,
  DriversRepository,
  NotificationsRepository,
  OrdersRepository,
  StatusHistoryRepository,
  UsersRepository,
} from 'src/common/repositories';

@Module({
  controllers: [DeliveriesController],
  providers: [
    DeliveriesService,
    NotificationService,
    NotificationsRepository,
    UsersRepository,
    DeliveryRepository,
    DriversRepository,
    OrdersRepository,
    StatusHistoryRepository,
  ],
  exports: [DeliveriesService],
})
export class DeliveriesModule {}
