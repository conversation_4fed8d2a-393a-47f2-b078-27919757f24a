import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { DriverStatus, ErrandStatus, OrderStatus } from '@prisma/client';
import { NotificationService } from '../notification/notification.service';

import { PaginationDto } from 'src/common/dto/pagination.dto';

import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { deliveryConfig } from 'src/config/delivery.config';
import { extractProductSourceCoordinates } from 'src/common/utils/pagination.util';
import { AssignDelivery } from './dto/assign.dto';
import { AcceptDeliveryDto } from './dto/accept-delivery.dto';
import {
  DeliveryRepository,
  DriversRepository,
  OrdersRepository,
  StatusHistoryRepository,
} from 'src/common/repositories';

@Injectable()
export class DeliveriesService {
  private readonly logger = new Logger(DeliveriesService.name);

  constructor(
    private notificationService: NotificationService,
    private readonly deliveryRepository: DeliveryRepository,
    private readonly ordersRepository: OrdersRepository,
    private readonly driversRepository: DriversRepository,
    private readonly statusHistoryRepository: StatusHistoryRepository,
  ) {}
  async getAllDeliveries(pagination: PaginationDto) {
    try {
      const { data: deliveries, meta } =
        await this.deliveryRepository.findAll(pagination);

      const formattedDeliveries = deliveries.map((delivery) => ({
        id: delivery.id,
        status: delivery.status,
        estimatedDistance: delivery.estimatedDistance,
        estimatedDuration: delivery.estimatedDuration,
        driver: delivery.currentDriver,
        customer: delivery.order.customer,
        dropoff: delivery.dropoffAddress,
        order: {
          id: delivery.order.id,
          items: delivery.order.items.map((item) => ({
            product: item.product.name,
            quantity: item.quantity,
            price: item.price,
          })),
          totalItems: delivery.order.items.reduce(
            (sum, item) => sum + item.quantity,
            0,
          ),
          totalAmount: delivery.order.totalAmount,
        },
        lastStatus: delivery.statusHistory[0],
      }));

      return {
        data: formattedDeliveries,
        meta,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getDeliveryById(id: string) {
    try {
      const delivery = await this.deliveryRepository.findOne(id);
      if (!delivery)
        throw new NotFoundException(`Delivery with ID ${id} not found`);

      return {
        ...delivery,
        totalAmount: delivery.order.totalAmount,
        pickupLocationCoordinates: extractProductSourceCoordinates(
          delivery.order.items,
        ),
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getDriverDailyDeliveries(driverId: string, pagination: PaginationDto) {
    try {
      const { data: deliveries, meta } =
        await this.deliveryRepository.getDriverDailyDeliveries(
          driverId,
          pagination,
        );

      const totalEarnings =
        deliveries.reduce(
          (sum, delivery) => sum + delivery.estimatedDistance,
          0,
        ) * deliveryConfig.costPerKm;

      const formattedDeliveries = deliveries.map((delivery) => ({
        id: delivery.id,
        status: delivery.status,
        estimatedDistance: delivery.estimatedDistance,
        estimatedDuration: delivery.estimatedDuration,
        earnings: delivery.estimatedDistance * deliveryConfig.costPerKm,
        customer: delivery.order.customer,
        dropoff: delivery.dropoffAddress,
        order: {
          id: delivery.order.id,
          items: delivery.order.items.map((item) => ({
            product: item.product.name,
            quantity: item.quantity,
            price: item.price,
          })),
          totalItems: delivery.order.items.reduce(
            (sum, item) => sum + item.quantity,
            0,
          ),
          totalAmount: delivery.order.totalAmount,
          productSourceCoordinates: extractProductSourceCoordinates(
            delivery.order.items,
          ),
        },

        lastStatus: delivery.statusHistory[0],
      }));

      return {
        data: formattedDeliveries,
        meta,
        totalEarnings,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getDriverDeliveries(
    driverId: string,
    pagination: PaginationDto,
    status?: ErrandStatus,
  ) {
    try {
      const { data: deliveries, meta } =
        await this.deliveryRepository.findByDriver(
          driverId,
          pagination,
          status,
        );

      const formattedDeliveries = deliveries.map((delivery) => ({
        id: delivery.id,
        status: delivery.status,
        estimatedDistance: delivery.estimatedDistance,
        estimatedDuration: delivery.estimatedDuration,

        dropoff: {
          ...delivery.dropoffAddress,
        },

        order: {
          id: delivery.order.id,
          customer: delivery.order.customer,
          items: delivery.order.items.map((item) => ({
            product: item.product.name,
            quantity: item.quantity,
            price: item.price,
          })),

          productSourceCoordinates: extractProductSourceCoordinates(
            delivery.order.items,
          ),

          totalItems: delivery.order.items.reduce(
            (sum, item) => sum + item.quantity,
            0,
          ),
          totalAmount: delivery.order.totalAmount,
        },

        lastStatus: delivery.statusHistory[0],
      }));

      return {
        data: formattedDeliveries,
        meta,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async updateDeliveryStatus(id: string, status: ErrandStatus) {
    try {
      const existingDelivery = await this.deliveryRepository.findOne(id);
      if (!existingDelivery) {
        throw new NotFoundException('Delivery not found');
      }
      const delivery = await this.deliveryRepository.updateStatus(id, status);
      return delivery;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async assignDriver(dto: AssignDelivery) {
    try {
      const { deliveryId, driverId } = dto;
      const driver = await this.driversRepository.findOne(driverId);
      if (!driver) throw new NotFoundException('Driver not found');
      if (
        driver.currentStatus !== DriverStatus.AVAILABLE ||
        !driver.isAvailable
      )
        throw new BadRequestException('Driver is not available');

      const existingDelivery =
        await this.deliveryRepository.findOne(deliveryId);

      if (existingDelivery.currentDriver.id) {
        throw new BadRequestException('Delivery already assigned');
      }

      const delivery = await this.deliveryRepository.updateCurrentDriver(
        deliveryId,
        driverId,
      );

      // Notify the assigned driver
      await this.notificationService.sendSingleNotification(
        delivery.currentDriver.id,
        'New Delivery Assignment',
        `You have been assigned to delivery #${deliveryId}`,
        {
          type: 'DELIVERY_ASSIGNMENT',
          deliveryId,
        },
      );

      await this.notificationService.sendSingleNotification(
        delivery.order.customer.id,
        'New Delivery Assignment',
        `You have been assigned to delivery #${deliveryId}`,
        {
          type: 'DELIVERY_ASSIGNMENT',
          deliveryId,
        },
      );

      return delivery;
    } catch (error) {
      this.logger.error(
        `Error assigning driver to delivery: ${error.message}`,
        error.stack,
      );
      handlePrismaError(error);
    }
  }

  async acceptDelivery(
    deliveryId: string,
    driverId: string,
    { latitude, longitude }: AcceptDeliveryDto,
  ) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) throw new NotFoundException('Delivery not found');

      const driver = await this.driversRepository.findByUserId(driverId);

      if (
        delivery.status !== ErrandStatus.PENDING &&
        delivery.status !== ErrandStatus.PENDING_PAYMENT &&
        delivery.status !== ErrandStatus.ASSIGNED
      )
        throw new BadRequestException(
          'Delivery is not available for acceptance',
        );

      const updatedDelivery = await this.deliveryRepository.update(deliveryId, {
        status: ErrandStatus.ACCEPTED,
        currentDriver: { connect: { id: driver.id } },
        currentLat: latitude,
        currentLong: longitude,
      });

      await this.driversRepository.update(driver.id, {
        currentStatus: DriverStatus.ON_ERRAND,
        assignedErrands: {
          connect: { id: deliveryId },
        },
        isAvailable: false,
      });

      if (delivery.order.customer.id) {
        await this.notificationService.sendSingleNotification(
          delivery.order.customer.id,
          'Delivery Driver Assigned',
          `A driver has been assigned to your delivery #${deliveryId}`,
          {
            type: 'DELIVERY_DRIVER_ASSIGNED',
            deliveryId: deliveryId,
            driverId: driverId,
          },
        );
      }

      await this.statusHistoryRepository.create({
        delivery: { connect: { id: deliveryId } },
        status: ErrandStatus.ACCEPTED,
        notes: `Driver ${updatedDelivery.currentDriver?.user?.fullname} accepted delivery`,
      });

      return updatedDelivery;
    } catch (error) {
      this.logger.error(
        `Error accepting delivery ${deliveryId} by driver ${driverId}: ${error.message}`,
        error.stack,
      );
      handlePrismaError(error);
    }
  }

  async updateDeliveryLocation({
    deliveryId,
    driverId,
    location,
  }: {
    deliveryId: string;
    driverId: string;
    location: { lat: number; lng: number };
  }) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) throw new NotFoundException('Delivery not found');

      if (delivery.currentDriver.id !== driverId)
        throw new ForbiddenException('You are not the assigned driver');

      const updatedDelivery =
        await this.deliveryRepository.updateCurrentLocation(
          deliveryId,
          location.lat,
          location.lng,
        );

      return updatedDelivery;
    } catch (error) {
      this.logger.error(
        `Error updating delivery location: ${error.message}`,
        error.stack,
      );
      handlePrismaError(error);
    }
  }

  async trackDeliveryLocation({
    deliveryId,
    driverId,
  }: {
    deliveryId: string;
    driverId: string;
  }) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) throw new NotFoundException('Delivery not found');

      if (delivery.currentDriver.id !== driverId)
        throw new ForbiddenException('You are not the assigned driver');

      return {
        lat: delivery.currentLat,
        lng: delivery.currentLong,
      };
    } catch (error) {
      this.logger.error(
        `Error tracking delivery location: ${error.message}`,
        error.stack,
      );
      handlePrismaError(error);
    }
  }

  async startDelivery(driverId: string, deliveryId: string) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) throw new NotFoundException('Delivery not found');

      if (delivery.currentDriver.user.id !== driverId) {
        throw new ForbiddenException('You are not the assigned driver');
      }

      if (delivery.status !== ErrandStatus.ACCEPTED)
        throw new BadRequestException(
          'Delivery must be in ACCEPTED status to start',
        );

      // update order to in progress
      await this.ordersRepository.update(delivery.order.id, {
        status: OrderStatus.IN_PROGRESS,
      });

      const updatedDelivery = await this.deliveryRepository.update(
        delivery.id,
        {
          status: ErrandStatus.IN_TRANSIT,
          startTime: new Date(),
        },
      );

      await this.statusHistoryRepository.create({
        delivery: { connect: { id: delivery.id } },
        status: ErrandStatus.IN_TRANSIT,
        notes: `Driver started the delivery`,
      });

      await this.notificationService.sendSingleNotification(
        delivery.order.customer.id,
        'Delivery Started',
        `Your delivery #${delivery.id} is now in transit`,
        {
          type: 'DELIVERY_STARTED',
          deliveryId: delivery.id,
        },
      );

      return updatedDelivery;
    } catch (error) {
      this.logger.error(
        `Error starting delivery: ${error.message}`,
        error.stack,
      );
      handlePrismaError(error);
    }
  }

  async endDelivery({
    deliveryId,
    customerId,
  }: {
    deliveryId: string;
    customerId: string;
  }) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) throw new NotFoundException('Delivery not found');

      if (delivery.order.customer.id !== customerId)
        throw new ForbiddenException(
          'You are not the customer of this delivery',
        );

      if (delivery.status !== ErrandStatus.IN_TRANSIT)
        throw new BadRequestException(
          'Delivery must be in IN_TRANSIT status to end',
        );

      const updatedDelivery = await this.deliveryRepository.update(deliveryId, {
        status: ErrandStatus.COMPLETED,
        endTime: new Date(),
      });

      await this.ordersRepository.update(delivery.order.id, {
        status: OrderStatus.DELIVERED,
      });

      await this.statusHistoryRepository.create({
        delivery: { connect: { id: delivery.id } },
        status: ErrandStatus.COMPLETED,
        notes: `Customer confirmed delivery completion`,
      });

      await this.driversRepository.update(delivery.currentDriver.id, {
        currentStatus: DriverStatus.AVAILABLE,
        isAvailable: true,
      });

      // Notify driver
      await this.notificationService.sendSingleNotification(
        delivery.currentDriver.user.id,
        'Delivery Completed',
        `Delivery #${deliveryId} has been completed`,
        {
          type: 'DELIVERY_COMPLETED',
          deliveryId,
        },
      );

      return updatedDelivery;
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
