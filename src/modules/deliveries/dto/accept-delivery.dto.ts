import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';

export class AcceptDeliveryDto {
  @ApiProperty({
    description: 'current location of the driver',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;
  @ApiProperty({
    description: 'current location of the driver',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;
}
