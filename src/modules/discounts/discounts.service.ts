import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { CreateDiscountDto } from './dto/create-discount.dto';
import { UpdateDiscountDto } from './dto/update-discount.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { DiscountRepository, UsersRepository } from 'src/common/repositories';
import { PaginationDto } from 'src/common/dto/pagination.dto';

@Injectable()
export class DiscountsService {
  constructor(
    private readonly discountRepository: DiscountRepository,
    private readonly usersRepository: UsersRepository,
  ) {}

  async create(createDiscountDto: CreateDiscountDto) {
    try {
      return this.discountRepository.create(createDiscountDto);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll(params: PaginationDto) {
    try {
      return this.discountRepository.findAll(params);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const discount = await this.discountRepository.findOne(id);

      if (!discount) {
        throw new NotFoundException(`Discount with ID ${id} not found`);
      }

      return discount;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(id: string, updateDiscountDto: UpdateDiscountDto) {
    try {
      await this.findOne(id);

      return this.discountRepository.update(id, updateDiscountDto);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string) {
    try {
      await this.findOne(id);

      return this.discountRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getDicount(code: string) {
    try {
      const discount = await this.discountRepository.findByCode(code);

      if (!discount) {
        throw new NotFoundException(`Discount code ${code} not found`);
      }

      return discount;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async validateDiscount(code: string, orderAmount: number) {
    try {
      const discount = await this.discountRepository.findByCode(code);

      if (!discount) {
        throw new NotFoundException(`Discount code ${code} not found`);
      }

      if (!discount.isActive) {
        throw new BadRequestException('This discount code is inactive');
      }

      const now = new Date();
      if (now < discount.startDate || now > discount.endDate) {
        throw new BadRequestException(
          'This discount code has expired or is not yet active',
        );
      }

      if (discount.usageLimit && discount.usageCount >= discount.usageLimit) {
        throw new BadRequestException(
          'This discount code has reached its usage limit',
        );
      }

      if (discount.minOrderAmount && orderAmount < discount.minOrderAmount) {
        throw new BadRequestException(
          `Order amount must be at least ${discount.minOrderAmount}`,
        );
      }

      let discountAmount = 0;
      if (discount.type === 'PERCENTAGE') {
        discountAmount = Math.round(orderAmount * discount.value * 100) / 100;
        if (discount.maxDiscount && discountAmount > discount.maxDiscount) {
          discountAmount = discount.maxDiscount;
        }
      } else {
        discountAmount = discount.value;
      }

      return { discount, discountAmount };
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
