import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  UseGuards,
  Query,
  NotFoundException,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DriversService } from './drivers.service';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { IsDriverGuard } from 'src/common/guards/isDriver.guard';
import { User } from 'src/common/decorators/user.decorator';
import { UpdateDriverStatusDto } from './dto/update-driver-status.dto';
import { IdValidationPipe } from 'src/common/pipes/id-validation.pipe';

@ApiTags('Drivers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('drivers')
export class DriversController {
  constructor(private readonly driverDetailsService: DriversService) {}

  @UseGuards(IsAdminGuard)
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new driver details entry' })
  @ApiBody({ type: CreateDriverDto })
  @ApiResponse({
    status: 201,
    description: 'The driver details has been successfully created.',
  })
  async create(@Body() createUserDto: CreateDriverDto) {
    return this.driverDetailsService.create(createUserDto);
  }

  @Get()
  @UseGuards(IsAdminGuard)
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiOperation({ summary: 'Retrieve all driver details' })
  @ApiResponse({ status: 200, description: 'List of all driver details' })
  async findAll(@Query() pagination: PaginationDto) {
    return this.driverDetailsService.findAll(pagination);
  }

  @Get('available')
  @ApiOperation({ summary: 'Retrieve all available driver details' })
  @ApiResponse({
    status: 200,
    description: 'List of all available driver details',
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async findAvailableDrivers(@Query() pagination: PaginationDto) {
    return this.driverDetailsService.findAvailableDrivers(pagination);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Retrieve driver details by ID' })
  @ApiResponse({ status: 200, description: 'Driver details found' })
  @ApiResponse({ status: 404, description: 'Driver details not found' })
  async findOne(@Param('id', new IdValidationPipe()) id: string) {
    return this.driverDetailsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Update driver details' })
  @ApiResponse({
    status: 200,
    description: 'Driver details successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Driver details not found' })
  async update(
    @Param('id', new IdValidationPipe()) id: string,
    @Body() updateDriverDetailsDto: UpdateDriverDto,
  ) {
    return this.driverDetailsService.update(id, updateDriverDetailsDto);
  }
  @Delete(':id')
  @UseGuards(IsAdminGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete driver details' })
  @ApiResponse({
    status: 200,
    description: 'Driver details successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Driver details not found' })
  async remove(@Param('id', new IdValidationPipe()) id: string) {
    return this.driverDetailsService.remove(id);
  }

  @Patch('status')
  @UseGuards(IsDriverGuard)
  @ApiOperation({ summary: 'Update driver status' })
  @ApiResponse({
    status: 200,
    description: 'Driver status successfully updated',
  })
  async updateStatus(
    @User() user,
    @Body() updateStatusDto: UpdateDriverStatusDto,
  ) {
    const driver = await this.driverDetailsService.findByUserId(user.userId);
    if (!driver) {
      throw new NotFoundException('Driver profile not found');
    }

    return this.driverDetailsService.updateDriverStatus(
      driver.id,
      updateStatusDto.status,
    );
  }
}
