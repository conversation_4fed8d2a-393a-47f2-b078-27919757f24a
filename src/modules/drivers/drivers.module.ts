import { Module } from '@nestjs/common';
import { DriversController } from './drivers.controller';
import { DriversService } from './drivers.service';
import { NotificationService } from '../notification/notification.service';
import {
  DriversRepository,
  NotificationsRepository,
  UsersRepository,
} from 'src/common/repositories';

@Module({
  imports: [],
  controllers: [DriversController],
  providers: [
    DriversService,
    UsersRepository,
    UsersRepository,
    NotificationsRepository,
    NotificationService,
    DriversRepository,
  ],
})
export class DriversModule {}
