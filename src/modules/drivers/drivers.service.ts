import { Injectable, NotFoundException } from '@nestjs/common';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { UserRole, DriverStatus } from '@prisma/client';
import { CreateDriverDto } from './dto/create-driver.dto';
import { UpdateDriverDto } from './dto/update-driver.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import * as bcrypt from 'bcrypt';

import { NotificationService } from '../notification/notification.service';
import { PaginatedResult } from 'src/common/interfaces';
import { DriversRepository } from 'src/common/repositories/drivers.repository';
import { Driver } from 'src/common/schemas';
import { UsersRepository } from 'src/common/repositories';
import { EmailService } from 'src/core/email/email.service';

@Injectable()
export class DriversService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly notificationService: NotificationService,
    private readonly driversRepository: DriversRepository,
    private readonly emailService: EmailService,
  ) {}

  async create(dto: CreateDriverDto) {
    const {
      email,
      password,
      fullname,
      licenseNumber,
      vehicleModel,
      vehicleType,
    } = dto;
    try {
      const existingDriver =
        await this.driversRepository.findByLinsenceNumber(licenseNumber);
      if (existingDriver)
        throw new NotFoundException(
          'Driver with this license number already exists',
        );

      const existingUser = await this.userRepository.getUserByEmail(email);
      if (existingUser)
        throw new NotFoundException('User with this email already exists');

      //hash password
      const hashedPassword = await bcrypt.hash(password, 10);
      const user = await this.userRepository.create({
        email,
        password: hashedPassword,
        fullname,
        role: UserRole.DRIVER,
        isEmailVerified: true,
        isPhoneVerified: true,
        isVerified: true,
      });
      const driverDetails = await this.driversRepository.create({
        user: { connect: { id: user.id } },
        licenseNumber,
        vehicleType,
        currentStatus: DriverStatus.AVAILABLE,
        vehicleModel,
      });

      await this.userRepository.update(user.id, {
        profile: {
          create: {
            gender: null,
            avatar: 'https://avatar.iran.liara.run/public/50',
            nickname: null,
          },
        },
      });

      await this.emailService.sendWelcomeEmail(email, fullname);

      await this.notificationService.sendSingleNotification(
        user.id,
        'Account Created',
        'Your account has been successfully created.',
        { type: 'ACCOUNT_CREATED' },
      );

      return { user, driverDetails };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll(pagination: PaginationDto): Promise<PaginatedResult<Driver>> {
    return this.driversRepository.findAll(pagination);
  }

  async findOne(id: string): Promise<any> {
    try {
      const driverDetails = await this.driversRepository.findOne(id);
      if (!driverDetails)
        throw new NotFoundException(`DriverDetails with id ${id} not found`);

      return driverDetails;
    } catch (err) {
      handlePrismaError(err);
    }
  }

  async update(id: string, updateDriverDetailsDto: UpdateDriverDto) {
    try {
      const driverDetails = await this.driversRepository.update(id, {
        ...updateDriverDetailsDto,
      });
      return driverDetails;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string): Promise<any> {
    const driverDetails = await this.findOne(id);
    if (!driverDetails)
      throw new NotFoundException(`DriverDetails with id ${id} not found`);
    return await this.driversRepository.remove(id);
  }

  async findAvailableDrivers(pagination: PaginationDto) {
    return this.driversRepository.findAvailableDrivers(pagination);
  }

  async setDriverAvailability(driverId: string, isAvailable: boolean) {
    const driver = await this.driversRepository.findOne(driverId);
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    return this.driversRepository.update(driverId, { isAvailable });
  }

  async findByUserId(userId: string) {
    try {
      const driver = await this.driversRepository.findByUserId(userId);
      if (!driver) {
        return null;
      }
      return driver;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async updateDriverStatus(driverId: string, status: DriverStatus) {
    try {
      // Get current driver details
      const currentDriver = await this.driversRepository.findOne(driverId);
      if (!currentDriver) {
        throw new NotFoundException(`Driver with ID ${driverId} not found`);
      }

      // Update driver status
      const updatedDriver = await this.driversRepository.update(driverId, {
        currentStatus: status,
      });

      return {
        success: true,
        driver: {
          id: updatedDriver.id,
          status: updatedDriver.currentStatus,
          isAvailable: updatedDriver.isAvailable,
          name: updatedDriver.user.fullname,
        },
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
