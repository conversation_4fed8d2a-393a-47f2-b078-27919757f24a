import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { VehicleType } from '@prisma/client';
import { CreateUserDto } from '../../users/dto/create-user.dto';

export class CreateDriverDto extends CreateUserDto {
  @ApiProperty({
    description: 'The license number of the driver',
    example: 'XYZ123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  licenseNumber?: string;

  @ApiProperty({
    description: 'The type of vehicle the driver uses',
    enum: VehicleType,
    required: false,
  })
  @IsOptional()
  @IsEnum(VehicleType)
  vehicleType?: VehicleType;

  @ApiProperty({
    description: 'The model of the vehicle the driver uses',
    example: 'Toyota Corolla',
    required: false,
  })
  @IsOptional()
  @IsString()
  vehicleModel?: string;
}
