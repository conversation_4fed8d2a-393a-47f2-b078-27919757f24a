import { IsEnum, IsOptional, IsBoolean, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum DriverStatus {
  AVAILABLE = 'AVAILABLE',
  OFFLINE = 'OFFLINE',
  UNAVAILABLE = 'UNAVAILABLE',
  ON_ERRAND = 'ON_ERRAND',
  ON_BREAK = 'ON_BREAK',
}

export class UpdateDriverStatusDto {
  @ApiProperty({
    enum: DriverStatus,
    description: 'New status for the driver',
  })
  @IsEnum(DriverStatus)
  status: DriverStatus;

  @ApiProperty({
    required: false,
    description: 'Explicitly set availability',
  })
  @IsOptional()
  @IsBoolean()
  isAvailable?: boolean;

  @ApiProperty({
    required: false,
    description: 'Additional notes about status change',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
