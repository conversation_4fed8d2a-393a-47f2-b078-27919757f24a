import { IsString, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendBulkNotificationDto {
  @ApiProperty({ description: 'Array of user IDs to send the notification to' })
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification body' })
  @IsString()
  body: string;

  @ApiProperty({ description: 'Additional data to send with the notification' })
  data?: Record<string, any>;
}
