import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  UseGuards,
  Patch,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { ExpoPushNotificationService } from './expo-push-notification.service';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { SendNotificationDto } from './dto/send.dto';
import { SendBulkNotificationDto } from './dto/send-bulk.dto';
import { UpdateTokenDto } from './dto/update-token.dto';

@ApiTags('Expo Push Notifications')
@Controller('expo-push-notification')
@ApiBearerAuth()
export class ExpoPushNotificationController {
  constructor(
    private readonly notificationService: ExpoPushNotificationService,
  ) {}

  @Post('send/:userId')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({
    summary: 'Send notification to a specific user by ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'ID of the user to send notification to',
    type: String,
  })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async sendNotificationById(
    @Param('userId') userId: string,
    @Body() notificationDto: SendNotificationDto,
  ) {
    return this.notificationService.sendSingleNotification(
      userId,
      notificationDto.title,
      notificationDto.body,
      notificationDto.data,
    );
  }

  @Post('send')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Send notification to a single user (Admin only)' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendNotification(
    @Body() notificationDto: SendNotificationDto,
    @User() user,
  ) {
    return this.notificationService.sendSingleNotification(
      user.userId,
      notificationDto.title,
      notificationDto.body,
      notificationDto.data,
    );
  }

  @Post('send-bulk')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({
    summary: 'Send notifications to multiple users (Admin only)',
  })
  @ApiResponse({ status: 201, description: 'Notifications sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async sendBulkNotifications(
    @Body() bulkNotificationDto: SendBulkNotificationDto,
  ) {
    if (!bulkNotificationDto.userIds?.length) {
      throw new BadRequestException('User IDs array cannot be empty');
    }

    return this.notificationService.sendBulkNotifications(
      bulkNotificationDto.userIds,
      bulkNotificationDto.title,
      bulkNotificationDto.body,
      bulkNotificationDto.data,
    );
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, description: 'Returns user notifications' })
  async getUserNotifications(
    @User() user,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
  ) {
    return this.notificationService.getUserNotifications(
      user.userId,
      page,
      limit,
    );
  }

  @Patch(':id/read')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markAsRead(@User() user, @Param('id') notificationId: string) {
    return this.notificationService.markAsRead(user.userId, notificationId);
  }

  @Patch('read-all')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllAsRead(@User() user) {
    return this.notificationService.markAllAsRead(user.userId);
  }

  @Post('push-token')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: "Update user's Expo push token" })
  @ApiResponse({ status: 200, description: 'Push token updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async updatePushToken(@User() user, @Body() { token }: UpdateTokenDto) {
    return this.notificationService.updateUserExpoPushToken(user.userId, token);
  }
}
