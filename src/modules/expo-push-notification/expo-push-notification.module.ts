import { Global, Module } from '@nestjs/common';
import { ExpoPushNotificationService } from './expo-push-notification.service';
import { ExpoPushNotificationController } from './expo-push-notification.controller';
import {
  NotificationsRepository,
  UsersRepository,
} from 'src/common/repositories';
@Global()
@Module({
  controllers: [ExpoPushNotificationController],
  providers: [
    ExpoPushNotificationService,
    UsersRepository,
    NotificationsRepository,
  ],
  exports: [ExpoPushNotificationService],
})
export class ExpoPushNotificationModule {}
