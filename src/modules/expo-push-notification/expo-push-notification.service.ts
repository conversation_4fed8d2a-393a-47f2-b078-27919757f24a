import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Expo, ExpoPushMessage, ExpoPushTicket } from 'expo-server-sdk';
import {
  NotificationsRepository,
  UsersRepository,
} from 'src/common/repositories';

@Injectable()
export class ExpoPushNotificationService {
  private expo: Expo;
  private readonly logger = new Logger(ExpoPushNotificationService.name);

  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly notificationsRepository: NotificationsRepository,
  ) {
    this.expo = new Expo();
  }

  private async sendNotifications(
    messages: ExpoPushMessage[],
  ): Promise<ExpoPushTicket[]> {
    const chunks = this.expo.chunkPushNotifications(messages);
    const tickets: ExpoPushTicket[] = [];

    try {
      for (const chunk of chunks) {
        try {
          const ticketChunk = await this.expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunk);
        } catch (error) {
          this.logger.error('Error sending chunk:', error);
        }
      }
      return tickets;
    } catch (error) {
      this.logger.error('Error sending notifications:', error);
      throw error;
    }
  }

  async sendSingleNotification(
    userId: string,
    title: string,
    body: string,
    data?: Record<string, any>,
  ) {
    const user = await this.usersRepository.findOne(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const notification = await this.notificationsRepository.create({
      user: { connect: { id: userId } },
      title,
      body,
      data,
      isRead: false,
      sentAt: new Date(),
    });

    // If user has Expo token, send push notification
    if (user.expoPushToken && Expo.isExpoPushToken(user.expoPushToken)) {
      const message: ExpoPushMessage = {
        to: user.expoPushToken,
        title,
        body,
        sound: 'default',
        data: {
          ...data,
          notificationId: notification.id,
        },
      };

      try {
        const tickets = await this.sendNotifications([message]);
        await this.checkNotificationStatus(tickets);
      } catch (error) {
        this.logger.error(
          `Failed to send push notification to user ${userId}:`,
          error,
        );
        // Note: We don't throw here as the notification is already created in the database
      }
    } else {
      this.logger.warn(`User ${userId} has no valid Expo push token`);
    }

    return notification;
  }

  async sendBulkNotifications(
    userIds: string[],
    title: string,
    body: string,
    data?: Record<string, any>,
  ) {
    const users = await this.usersRepository.findManyUsersById(userIds);

    if (!users.length) {
      throw new NotFoundException('No users found');
    }
    const notifications =
      await this.notificationsRepository.createManyWithUserId(
        users.map((user) => user.id),
        {
          title,
          body,
          data,
          isRead: false,
          sentAt: new Date(),
        },
      );
    // Prepare push notifications for users with valid Expo tokens
    const messages: ExpoPushMessage[] = users
      .filter(
        (user) =>
          user.expoPushToken && Expo.isExpoPushToken(user.expoPushToken),
      )
      .map((user, index) => ({
        to: user.expoPushToken!,
        title,
        body,
        sound: 'default',
        data: {
          ...data,
          notificationId: notifications[index].id,
          userId: user.id,
        },
      }));

    if (messages.length > 0) {
      try {
        const tickets = await this.sendNotifications(messages);
        await this.checkNotificationStatus(tickets);
      } catch (error) {
        this.logger.error('Failed to send bulk push notifications:', error);
        // Note: We don't throw here as the notifications are already created in the database
      }
    }

    return notifications;
  }

  async checkNotificationStatus(tickets: ExpoPushTicket[]) {
    const receiptIds = tickets
      .filter(
        (ticket): ticket is ExpoPushTicket & { id: string } =>
          ticket.status !== undefined,
      )
      .map((ticket) => ticket.id);

    if (!receiptIds.length) return;

    const receiptIdChunks =
      this.expo.chunkPushNotificationReceiptIds(receiptIds);

    try {
      for (const chunk of receiptIdChunks) {
        const receipts =
          await this.expo.getPushNotificationReceiptsAsync(chunk);

        for (const receiptId in receipts) {
          const receipt = receipts[receiptId];

          if (receipt.status === 'error') {
            this.logger.error(
              `Error delivering notification: ${receipt.message}`,
              receipt.details,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error('Error checking receipts:', error);
      throw error;
    }
  }

  async updateUserExpoPushToken(userId: string, token: string) {
    if (!Expo.isExpoPushToken(token)) {
      throw new Error('Invalid Expo push token');
    }

    return this.usersRepository.updateExpoToken(userId, {
      expoPushToken: token,
    });
  }

  async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    return this.notificationsRepository.findAllByUser(userId, {
      page,
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }

  async markAsRead(userId: string, notificationId: string) {
    const notification =
      await this.notificationsRepository.findOne(notificationId);
    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return this.notificationsRepository.markAsRead(userId, notificationId);
  }

  async markAllAsRead(userId: string): Promise<void> {
    return this.notificationsRepository.markAllAsRead(userId);
  }
}
