import { Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';

import { ProfileModule } from './profile/profile.module';
import { OrdersModule } from './orders/orders.module';
import { ProductCategoriesModule } from './product-categories/product-categories.module';
import { ProductSourceModule } from './product-source/product-source.module';
import { DriversModule } from './drivers/drivers.module';
import { ProductSourceCategoriesModule } from './product-source-categories/product-source-categories.module';
import { AddressModule } from './address/address.module';
import { AdminModule } from './admin/admin.module';
import { NotificationModule } from './notification/notification.module';

import { SettingsModule } from './settings/settings.module';
import { ChatModule } from './chat/chat.module';
import { DeliveriesModule } from './deliveries/deliveries.module';
import { TrackingModule } from './tracking/tracking.module';
import { ExpoPushNotificationModule } from './expo-push-notification/expo-push-notification.module';
import { ProductsModule } from './products/products.module';
import { DiscountsModule } from './discounts/discount.module';
import { ReviewsModule } from './reviews/reviews.module';
import { PaymentsModule } from './payments/payments.module';

@Module({
  imports: [
    AuthModule,
    UsersModule,
    ProfileModule,
    OrdersModule,
    ProductCategoriesModule,
    ProductSourceModule,
    ProductsModule,
    DriversModule,
    ProductSourceCategoriesModule,
    AddressModule,
    AdminModule,
    NotificationModule,
    SettingsModule,
    ChatModule,
    DeliveriesModule,
    TrackingModule,
    DiscountsModule,
    ExpoPushNotificationModule,
    ReviewsModule,
    PaymentsModule,
  ],
})
export class ModulesModule {}
