import { IsString, IsOptional, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DeliveryNotificationDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  message: string;

  @ApiProperty({ required: false })
  @IsObject()
  @IsOptional()
  metadata?: {
    type: string;
    deliveryId: string;
    driverId?: string;
    driverName?: string;
    status?: string;
    notes?: string;
  };
}
