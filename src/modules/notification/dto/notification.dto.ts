import { IsString, IsOptional, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendNotificationDto {
  @ApiProperty({ description: 'User ID to send the notification to' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification body' })
  @IsString()
  body: string;

  @ApiPropertyOptional({
    description: 'Additional data to send with the notification',
  })
  @IsOptional()
  data?: Record<string, string>;
}

export class SendBulkNotificationDto {
  @ApiProperty({ description: 'Array of user IDs to send the notification to' })
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification body' })
  @IsString()
  body: string;

  @ApiPropertyOptional({
    description: 'Additional data to send with the notification',
  })
  @IsOptional()
  data?: Record<string, string>;
}

export class TopicNotificationDto {
  @ApiProperty({ description: 'Topic to send the notification to' })
  @IsString()
  topic: string;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification body' })
  @IsString()
  body: string;

  @ApiPropertyOptional({
    description: 'Additional data to send with the notification',
  })
  @IsOptional()
  data?: Record<string, string>;
}

export class TopicSubscriptionDto {
  @ApiProperty({ description: 'Array of user IDs to subscribe/unsubscribe' })
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @ApiProperty({ description: 'Topic name' })
  @IsString()
  topic: string;
}
