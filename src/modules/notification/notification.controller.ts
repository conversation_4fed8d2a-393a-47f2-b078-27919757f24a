import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  Body,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';
import {
  SendNotificationDto,
  SendBulkNotificationDto,
} from './dto/notification.dto';
import { NotificationService } from './notification.service';
import { PaginationDto } from 'src/common/dto/pagination.dto';

@ApiTags('notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiResponse({ status: 200, description: 'Returns user notifications' })
  @ApiResponse({ status: 404, description: 'Notifications not found' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'isRead', required: false, type: Boolean })
  async getUserNotifications(@User() user, @Query() pagination: PaginationDto) {
    return this.notificationService.getUserNotifications(
      user.userId,
      pagination,
    );
  }

  @Get('unread-count')
  @ApiOperation({ summary: 'Get unread notifications count' })
  @ApiResponse({
    status: 200,
    description: 'Returns unread notifications count',
  })
  async getUnreadCount(@User() user) {
    return this.notificationService.getUnreadCount(user.userId);
  }

  @Post(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  async markAsRead(@User() user, @Param('id') notificationId: string) {
    return this.notificationService.markAsRead(user.userId, notificationId);
  }

  @Post('read-all')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllAsRead(@User() user) {
    return this.notificationService.markAllAsRead(user.userId);
  }

  @Delete()
  @ApiOperation({ summary: 'Delete all notifications' })
  @ApiResponse({ status: 200, description: 'All notifications deleted' })
  async deleteAllNotifications(@User() user) {
    return this.notificationService.deleteAllNotifications(user.userId);
  }

  @Post('send-bulk')
  @ApiOperation({ summary: 'Send bulk notifications' })
  @ApiResponse({
    status: 201,
    description: 'The notifications have been successfully sent.',
  })
  async sendBulkNotifications(
    @Body(ValidationPipe) dto: SendBulkNotificationDto,
  ) {
    return this.notificationService.sendBulkNotifications(
      dto.userIds,
      dto.title,
      dto.body,
      dto.data,
    );
  }

  @Post('send-topic')
  @ApiOperation({ summary: 'Send a topic notification' })
  @ApiResponse({
    status: 201,
    description: 'The topic notification has been successfully sent.',
  })
  @Post('send')
  @ApiOperation({ summary: 'Send a single notification' })
  @ApiResponse({
    status: 201,
    description: 'The notification has been successfully sent.',
  })
  async sendNotification(@Body(ValidationPipe) dto: SendNotificationDto) {
    return this.notificationService.sendSingleNotification(
      dto.userId,
      dto.title,
      dto.body,
      dto.data,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({ status: 200, description: 'Notification deleted' })
  async deleteNotification(@User() user, @Param('id') notificationId: string) {
    return this.notificationService.deleteNotification(
      user.userId,
      notificationId,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a single notification' })
  @ApiResponse({
    status: 200,
    description: 'Returns the notification details',
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found',
  })
  async getSingleNotification(
    @User() user,
    @Param('id') notificationId: string,
  ) {
    return this.notificationService.getSingleNotification(
      user.userId,
      notificationId,
    );
  }

  @Post('test-email')
  @ApiOperation({ summary: 'Send a test email' })
  @ApiResponse({
    status: 200,
    description: 'Email sent',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          description: 'Email address to send the test email to',
        },
      },
    },
  })
  async testEmail(@Body() { email }: { email: string }) {
    return this.notificationService.TestEmail(email);
  }
}
