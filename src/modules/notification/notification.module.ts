import { Global, Module } from '@nestjs/common';
import { NotificationsController } from './notification.controller';
import { NotificationService } from './notification.service';
import { ExpoPushNotificationModule } from '../expo-push-notification/expo-push-notification.module';
import { ExpoPushNotificationService } from '../expo-push-notification/expo-push-notification.service';
import { EmailService } from 'src/core/email/email.service';
import {
  NotificationsRepository,
  UsersRepository,
} from 'src/common/repositories';

@Global()
@Module({
  imports: [ExpoPushNotificationModule],
  controllers: [NotificationsController],
  providers: [
    NotificationService,
    EmailService,
    ExpoPushNotificationService,
    NotificationsRepository,
    UsersRepository,
  ],
  exports: [NotificationService],
})
export class NotificationModule {}
