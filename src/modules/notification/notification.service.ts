import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import { ExpoPushNotificationService } from '../expo-push-notification/expo-push-notification.service';
import { EmailService } from 'src/core/email/email.service';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import {
  NotificationsRepository,
  UsersRepository,
} from 'src/common/repositories';

@Injectable()
export class NotificationService {
  constructor(
    private readonly expoPushService: ExpoPushNotificationService,
    private readonly emailService: EmailService,
    private readonly usersRepository: UsersRepository,
    private readonly notificationsRepository: NotificationsRepository,
  ) {}

  async getUserNotifications(
    userId: string,
    pagination: PaginationDto,
    isRead?: boolean,
  ) {
    try {
      const user = await this.usersRepository.findOne(userId);

      if (!user) throw new NotFoundException('User not found');
      return this.notificationsRepository.findAllByUser(
        userId,
        pagination,
        isRead,
      );
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getUnreadCount(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new NotFoundException('User not found');
      return this.notificationsRepository.count({
        where: { userId, isRead: false },
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }
  async markAsRead(userId: string, notificationId: string) {
    const user = await this.usersRepository.findOne(userId);

    if (!user) throw new NotFoundException('User not found');
    const notification =
      await this.notificationsRepository.findOne(notificationId);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return this.notificationsRepository.markAsRead(userId, notificationId);
  }

  async markAllAsRead(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user) throw new NotFoundException('User not found');
      return this.notificationsRepository.markAllAsRead(userId);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async deleteNotification(userId: string, notificationId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);

      if (!user) throw new NotFoundException('User not found');
      const notification =
        await this.notificationsRepository.findOne(notificationId);
      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      return this.notificationsRepository.remove(notificationId);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async deleteAllNotifications(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);

      if (!user) throw new NotFoundException('User not found');
      return this.notificationsRepository.deleteAllNotifications(userId);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async sendSingleNotification(
    userId: string,
    title: string,
    body: string,
    data?: Record<string, string>,
  ) {
    const user = await this.usersRepository.findOne(userId);

    if (!user) throw new NotFoundException('User not found');
    const notification = await this.expoPushService.sendSingleNotification(
      userId,
      title,
      body,
      data,
    );

    return notification;
  }

  async sendBulkNotifications(
    userIds: string[],
    title: string,
    body: string,
    data?: Record<string, string>,
  ) {
    const users = await this.usersRepository.findManyUsersById(userIds);

    if (users.length !== userIds.length) {
      throw new NotFoundException('One or more users not found');
    }

    const notifications = await this.expoPushService.sendBulkNotifications(
      userIds,
      title,
      body,
      data,
    );

    return notifications;
  }

  async getSingleNotification(userId: string, notificationId: string) {
    const user = await this.usersRepository.findOne(userId);

    if (!user) throw new NotFoundException('User not found');
    const notification =
      await this.notificationsRepository.findOne(notificationId);

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return notification;
  }

  async findAll(userId: string, pagination: PaginationDto) {
    try {
      const user = await this.usersRepository.findOne(userId);

      if (!user) throw new NotFoundException('User not found');
      return this.notificationsRepository.findAllByUser(userId, pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async TestEmail(email: string) {
    return this.emailService.sendUserOtp(
      email,
      'Charles',
      '123456',
      'Your One-Time Password (OTP) for Verification',
      'We received a request to verify your account. Please use the One-Time Password (OTP) below to complete the verification process:',
    );
  }

  async sendEmailNotification() {
    // todo
  }

  async sendSmsNotification() {
    // todo
  }
}
