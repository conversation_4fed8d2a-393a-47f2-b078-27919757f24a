import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class OrderItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  productSourceId?: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  price: number;
}

class DeliveryDetailsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  dropoffAddressId: string;

  @ApiProperty({ description: 'selected or current drivers id' })
  @IsNotEmpty()
  @IsString()
  driverId: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  fee: number;
}

export class PaymentDetailsDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  mobileNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  provider?: string;
}

export class CreateOrderDto {
  @ApiProperty({ description: 'selected user order details' })
  @IsOptional()
  @IsString()
  orderPreference?: string;

  @ApiProperty({ description: 'selected user order details', required: false })
  @IsOptional()
  @IsString()
  couponCode?: string;

  @ApiProperty({ description: 'selected user order details' })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({ type: [OrderItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({ type: DeliveryDetailsDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => DeliveryDetailsDto)
  deliveryDetails?: DeliveryDetailsDto;

  @ApiProperty({ type: PaymentDetailsDto })
  @ValidateNested()
  @Type(() => PaymentDetailsDto)
  @IsNotEmpty()
  paymentDetails: PaymentDetailsDto;
}

export class InitiateOrder {
  @ApiProperty({ type: [OrderItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({ description: 'selected user order details' })
  @IsNotEmpty()
  @IsString()
  orderPreference: string;

  @ApiProperty({ description: 'selected user order details' })
  addressId: string;
}

export class CancelOrder {
  @ApiProperty({ description: 'reason for cancellation' })
  @IsNotEmpty()
  @IsString()
  reason: string;
}
