import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class OrderItemDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  quantity: number;
}

class DirectLocationDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  latitude: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  longitude: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  label?: string;
}

export class DirectLocationOrderDto {
  @ApiProperty({ enum: ['DELIVERY', 'PICKUP'] })
  @IsEnum(['DELIVERY', 'PICKUP'])
  orderPreference: string;

  @ApiProperty({ type: [OrderItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];
  @ApiProperty({ description: 'selected or current drivers id' })
  @IsNotEmpty()
  @IsString()
  driverId: string;

  @ApiProperty({ type: DirectLocationDto })
  @ValidateNested()
  @Type(() => DirectLocationDto)
  pickupLocation: DirectLocationDto;

  @ApiProperty({ type: DirectLocationDto })
  @ValidateNested()
  @Type(() => DirectLocationDto)
  dropoffLocation: DirectLocationDto;
}
