import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class RateOrderDto {
  @ApiProperty({ description: 'Rating for the driver' })
  @IsNotEmpty()
  @IsNumber()
  rating: number;

  @ApiProperty({
    description: 'Optional comment for the rating',
    required: false,
  })
  @IsOptional()
  @IsString()
  comment?: string;
}
