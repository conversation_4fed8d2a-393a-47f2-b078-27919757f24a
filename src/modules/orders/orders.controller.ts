import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  UseGuards,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto, InitiateOrder } from './dto/create-order.dto';

import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { OrderStatus } from '@prisma/client';
import { User } from 'src/common/decorators/user.decorator';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { IsDriverGuard } from 'src/common/guards/isDriver.guard';
import { RetryOrderDto } from './dto/retry-order.dto';
import { RateOrderDto } from './dto/rate.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get()
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all orders (admin only)' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    description: 'Filter by order status',
  })
  @ApiQuery({
    name: 'customerId',
    required: false,
    description: 'Filter by customer ID',
  })
  @ApiQuery({
    name: 'driverId',
    required: false,
    description: 'Filter by driver ID',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Filter by start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'Filter by end date (ISO string)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort direction',
  })
  getAllOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: OrderStatus,
    @Query('customerId') customerId?: string,
    @Query('driverId') driverId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('sortBy', new DefaultValuePipe('createdAt')) sortBy?: string,
  ) {
    return this.ordersService.getAllOrdersPaginated({
      page,
      limit,
      status,
      customerId,
      driverId,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      sortBy,
    });
  }

  @Post('initiate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Initiate a new order' })
  async initiateOrder(@User() user, @Body() body: InitiateOrder) {
    return this.ordersService.createOrderIntent(body, user.userId);
  }

  @Post('retry')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Retry an order payment' })
  retryOrderPayment(@Body() dto: RetryOrderDto) {
    return this.ordersService.retryOrderPayment(dto);
  }

  @Get('users-orders')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get paginated orders for the current user' })
  findAll(@User() user, @Query() pagination: PaginationDto) {
    return this.ordersService.getCustomerOrdersPaginated(
      user.userId,
      pagination,
    );
  }

  @Get('status/:status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders by status for the current user' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  findByStatus(
    @User() user,
    @Param('status') status: OrderStatus,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return this.ordersService.getOrdersByStatus(user.userId, status, {
      page,
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }

  @Get('driver/:driverId')
  @UseGuards(JwtAuthGuard, IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders assigned to a specific driver' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: OrderStatus })
  getDriverOrders(
    @Param('driverId') driverId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: OrderStatus,
  ) {
    return this.ordersService.getDriverOrdersPaginated(driverId, {
      page,
      limit,
      status,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }

  @Get('driver/current')
  @UseGuards(JwtAuthGuard, IsDriverGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders assigned to the current driver' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: OrderStatus })
  getCurrentDriverOrders(
    @User() user,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: OrderStatus,
  ) {
    return this.ordersService.getCurrentDriverOrders(user.userId, {
      page,
      limit,
      status,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order statistics (admin only)' })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  getOrderStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.ordersService.getOrderStatistics(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('daily')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get daily orders with statistics' })
  @ApiQuery({
    name: 'date',
    required: false,
    type: String,
    description: 'Target date in YYYY-MM-DD format. Defaults to current date',
  })
  async getDailyOrders(@Query('date') date?: string) {
    return this.ordersService.getDailyOrders(date);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by ID' })
  findOne(@User() user, @Param('id') id: string) {
    return this.ordersService.getOrderById(id);
  }
  @Get('customer/:customerId')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get orders for a specific customer (admin only)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: OrderStatus })
  getCustomerOrders(
    @Param('customerId') customerId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('status') status?: OrderStatus,
  ) {
    return this.ordersService.getCustomerOrdersPaginated(customerId, {
      page,
      limit,
      status,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update order status' })
  updateStatus(
    @User() user,
    @Param('id') id: string,
    @Body() body: { status: OrderStatus; notes?: string },
  ) {
    return this.ordersService.updateOrderStatus(
      id,
      body.status,
      user.userId,
      user.role,
      body.notes,
    );
  }

  @Patch(':id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel an order' })
  cancelOrder(
    @User() user,
    @Param('id') id: string,
    @Body() body: { reason: string },
  ) {
    return this.ordersService.cancelOrder(
      id,
      user.userId,
      user.role,
      body.reason,
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete an order (admin only)' })
  remove(@Param('id') id: string) {
    return this.ordersService.removeOrder(id);
  }

  @Get(':id/timeline')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order timeline/history' })
  getOrderTimeline(@Param('id') id: string) {
    return this.ordersService.getOrderTimeline(id);
  }

  @Post(':id/rate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Rate an driver for an order/delivery' })
  rateOrder(@User() user, @Param('id') id: string, @Body() body: RateOrderDto) {
    return this.ordersService.rateOrder(
      id,
      user.userId,
      body.rating,
      body.comment,
    );
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or validation error',
  })
  async createOrder(@User() user, @Body() createOrderDto: CreateOrderDto) {
    return this.ordersService.createMultiPickupOrder(
      user.userId,
      createOrderDto,
    );
  }
}
