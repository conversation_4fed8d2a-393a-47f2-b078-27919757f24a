import { Module, forwardRef } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { CampayModule } from 'src/core/campay/campay.module';
import { EmailService } from 'src/core/email/email.service';
import {
  DiscountRepository,
  DriversRepository,
  OrdersRepository,
  ProductRepository,
  UsersRepository,
  AddressRepository,
  PaymentRepository,
  DeliveryRepository,
  ReviewsRepository,
  PaymentAccountsRepository,
} from 'src/common/repositories';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [forwardRef(() => CampayModule), NotificationModule],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    UsersRepository,
    PaymentAccountsRepository,
    ProductRepository,
    EmailService,
    AddressRepository,
    DriversRepository,
    PaymentRepository,
    DeliveryRepository,
    DiscountRepository,
    OrdersRepository,
    UsersRepository,
    ReviewsRepository,
  ],
  exports: [OrdersService],
})
export class OrdersModule {}
