import { Driver } from './../../common/schemas/drivers.schema';
import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { deliveryConfig } from '../../config/delivery.config';
import { CreateOrderDto, InitiateOrder } from './dto/create-order.dto';
import { OrderStatus, ErrandStatus, UserRole } from '@prisma/client';
import { extractProductSourceCoordinates } from 'src/common/utils/pagination.util';
import { NotificationService } from '../notification/notification.service';
import {
  getDistance,
  getLatitude,
  getLongitude,
  orderByDistance,
} from 'geolib';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { CampayService } from 'src/core/campay/campay.service';
import { GeoPoint, PaginationParams } from 'src/common/interfaces/orders';
import { RetryOrderDto } from './dto/retry-order.dto';
import { EmailService } from 'src/core/email/email.service';
import {
  AddressRepository,
  DiscountRepository,
  DriversRepository,
  OrdersRepository,
  ProductRepository,
  PaymentRepository,
  DeliveryRepository,
  UsersRepository,
  ReviewsRepository,
  PaymentAccountsRepository,
} from 'src/common/repositories';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { Discount, OrderSelect } from 'src/common/schemas';
import { PrismaService } from 'src/core/prisma/prisma.service';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    private notificationService: NotificationService,
    private readonly discountRepository: DiscountRepository,
    private readonly paymentAccountRepository: PaymentAccountsRepository,
    private readonly usersRepository: UsersRepository,
    private readonly driversRepository: DriversRepository,
    private readonly ordersRepository: OrdersRepository,
    private readonly addressRepository: AddressRepository,
    private readonly prisma: PrismaService,
    private readonly deliveryRepository: DeliveryRepository,
    private readonly productRepository: ProductRepository,
    private campayService: CampayService,
    private reviewsRepository: ReviewsRepository,
    private emailService: EmailService,
    private readonly paymentRepository: PaymentRepository,
  ) {}

  private calculatePickupDistance(pickupPoints: GeoPoint[]): number {
    if (pickupPoints.length <= 1) {
      return 0;
    }

    const orderedPoints = orderByDistance(
      pickupPoints[0],
      pickupPoints.slice(1),
    );
    let totalDistance = 0;
    let previousPoint = pickupPoints[0];

    for (const point of orderedPoints) {
      totalDistance += getDistance(previousPoint, point);
      previousPoint = {
        latitude: getLatitude(point),
        longitude: getLongitude(point),
      };
    }

    return Math.round((totalDistance / 1000) * 100) / 100;
  }

  private async validateProducts(items: CreateOrderDto['items']) {
    const productIds = items.map((item) => item.productId);
    if (!productIds.length) {
      throw new BadRequestException('No products in order');
    }
    const products = await this.productRepository.findMany(productIds);
    if (products.length !== productIds.length) {
      throw new BadRequestException('One or more products not found');
    }

    return products;
  }

  async createMultiPickupOrder(userId: string, createOrderDto: CreateOrderDto) {
    const timeout = 20000;
    let discount: Discount;
    try {
      const [customer, driver] = await Promise.all([
        this.usersRepository.findOne(userId),
        createOrderDto.deliveryDetails?.driverId
          ? this.driversRepository.findOne(
              createOrderDto.deliveryDetails.driverId,
            )
          : null,
      ]);

      if (!customer) throw new BadRequestException('User not found');

      if (driver) {
        if (
          driver?.currentStatus !== 'AVAILABLE' &&
          driver?.currentStatus !== 'PENDING_DELIVERY'
        )
          throw new BadRequestException('Driver is not available');
      } else throw new BadRequestException('Driver not found');

      const products = await this.validateProducts(createOrderDto.items);
      const productMap = new Map(products.map((p) => [p.id, p]));

      const itemsByLocation = this.groupItemsByLocation(
        createOrderDto.items,
        productMap,
      );

      const dropoffAddress = await this.addressRepository.findOne(
        createOrderDto.deliveryDetails.dropoffAddressId,
      );
      if (!dropoffAddress)
        throw new BadRequestException('Invalid dropoff address');

      const pickupLocations = this.getPickupLocations(itemsByLocation);
      const { totalDistance } = this.calculateDeliveryFees(
        pickupLocations,
        dropoffAddress,
      );

      const orderTotal = createOrderDto.amount;

      if (createOrderDto.couponCode) {
        discount = await this.discountRepository.findByCode(
          createOrderDto.couponCode,
        );
      }

      const result = await this.prisma.$transaction(
        async (tx) => {
          const orderData: any = {
            status: OrderStatus.PENDING_PAYMENT,
            totalAmount: orderTotal,
            orderPreference: createOrderDto.orderPreference,
            customer: { connect: { id: userId } },
            isPayed: false,
          };

          // Only add discount if it exists
          if (discount) {
            orderData.discount = { connect: { id: discount.id } };
          }

          const order = await tx.order.create({
            data: orderData,
            select: OrderSelect,
          });

          await tx.orderItem.createMany({
            data: createOrderDto.items.map((item) => ({
              orderId: order.id,
              productId: item.productId,
              quantity: item.quantity,
              price: productMap.get(item.productId).price * item.quantity,
            })),
          });

          const delivery = await tx.delivery.create({
            data: {
              status: ErrandStatus.PENDING_PAYMENT,
              dropoffAddress: { connect: { id: dropoffAddress.id } },
              pickupAddresses: pickupLocations.map((loc) => loc.id),
              ...(driver && {
                currentDriver: { connect: { id: driver.id } },
              }),
              order: { connect: { id: order.id } },
              statusHistory: {
                create: {
                  status: ErrandStatus.PENDING_PAYMENT,
                  notes: driver
                    ? `Assigned to driver ${driver.user.fullname}`
                    : 'Order created, awaiting driver assignment',
                },
              },
              estimatedDistance: totalDistance,
              fee: createOrderDto.deliveryDetails.fee,
              estimatedDuration: Math.round(totalDistance * 3 * 60),
            },
          });

          if (driver) {
            await this.updateDriverStatus(tx, driver);
          }

          await this.notifyCustomer(userId, order.id);
          // await this.emailService.sendOrderConfirmationEmail(
          //   customer.email,
          //   order,
          // );

          return { order, delivery, discount };
        },
        { timeout },
      );

      this.campayService.validatePaymentInfo(
        userId,
        createOrderDto.paymentDetails.mobileNumber,
      );

      this.logger.log(`Order created: ${result.order.id}`);

      // request payment
      const payment = await this.campayService.requestPayment(
        {
          amount: result.order.totalAmount.toString(),
          from: createOrderDto.paymentDetails.mobileNumber,
          description: `Order payment for ${customer.fullname}`,
          external_reference: `ORDER_${Date.now()}`,
          email: customer.email,
          order: result.order,
        },
        userId,
      );

      return { order: result.order, delivery: result.delivery, payment };
    } catch (error) {
      console.log(error);
      this.logger.error('Error creating multi-pickup order:', error);
      throw handlePrismaError(error);
    }
  }

  private groupItemsByLocation(
    items: CreateOrderDto['items'],
    productMap: Map<string, any>,
  ) {
    const itemsByLocation = new Map();

    for (const item of items) {
      const product = productMap.get(item.productId);
      if (!product) {
        throw new BadRequestException(`Product not found: ${item.productId}`);
      }

      const sourceId = item.productSourceId || product.productSourceId;
      if (!sourceId) {
        throw new BadRequestException(
          `No source location for product: ${product.name}`,
        );
      }

      if (!itemsByLocation.has(sourceId)) {
        itemsByLocation.set(sourceId, {
          sourceId,
          sourceData: product.productSource,
          items: [],
        });
      }

      itemsByLocation.get(sourceId).items.push({
        product,
        quantity: item.quantity,
        price: product.price * item.quantity,
      });
    }

    return itemsByLocation;
  }

  private getPickupLocations(itemsByLocation: Map<string, any>) {
    return Array.from(itemsByLocation.values()).map((loc) => ({
      id: loc.sourceId,
      name: loc.sourceData.name,
      latitude: Number(loc.sourceData.lat),
      longitude: Number(loc.sourceData.long),
      items: loc.items,
    }));
  }

  private calculateDeliveryFees(pickupLocations: any[], dropoffAddress: any) {
    const pickupDistance = this.calculatePickupDistance(
      pickupLocations.map((loc) => ({
        latitude: loc.latitude,
        longitude: loc.longitude,
      })),
    );

    const lastPickup = pickupLocations[pickupLocations.length - 1];
    const deliveryDistance =
      getDistance(
        { latitude: lastPickup.latitude, longitude: lastPickup.longitude },
        {
          latitude: Number(dropoffAddress.latitude),
          longitude: Number(dropoffAddress.longitude),
        },
      ) / 1000;

    const totalDistance = pickupDistance + deliveryDistance;
    const pickupFee = (pickupLocations.length - 1) * deliveryConfig.baseFee;
    const distanceFee = totalDistance * deliveryConfig.costPerKm;
    const baseFee = deliveryConfig.baseFee;
    const totalDeliveryFee = Math.max(
      deliveryConfig.minimumFee,
      Math.round((baseFee + pickupFee + distanceFee) * 100) / 100,
    );

    return { totalDistance, totalDeliveryFee };
  }

  private async updateDriverStatus(tx: any, driver: Driver) {
    return tx.driverDetails.update({
      where: { userId: driver.user.id },
      data: { currentStatus: 'ON_ERRAND', isAvailable: false },
    });
  }

  private async notifyCustomer(customerId: string, orderId: string) {
    return this.notificationService.sendSingleNotification(
      customerId,
      'Order is to be paid',
      `Your order #${orderId} payment is to be validated`,
      {
        type: 'PENDING_PAYMENT',
        orderId,
      },
    );
  }

  private formatOrderResponse(result: any) {
    return {
      orderId: result.order.id,
      deliveryId: result.delivery.id,
      paymentStatus: 'PENDING',
      discountApplied: result.discountData
        ? {
            code: 'FIRSTORDER',
            type: 'PERCENTAGE',
            value: 15,
            discountAmount: result.discountData.discountAmount,
          }
        : null,
    };
  }
  async getOrderById(id: string) {
    const order = await this.ordersRepository.findOne(id);
    if (!order) throw new NotFoundException(`Order with ID ${id} not found`);

    const itemsTotal = order.items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0,
    );

    return {
      orderId: order.id,
      orderStatus: order.status,
      createdAt: order.createdAt,
      customer: {
        id: order.customer.id,
        name: order.customer.fullname,
        phone: order.customer.phone,
        email: order.customer.email,
      },
      items: order.items.map((item) => ({
        id: item.id,
        productName: item.product.name,
        quantity: item.quantity,
        unitPrice: item.price,
        subtotal: item.price * item.quantity,
      })),

      delivery: {
        id: order.delivery.id,
        status: order.delivery.status,
        driver: order.delivery.currentDriver,
        dropoffAddress: order.delivery.dropoffAddress,
        currentLocation: {
          latitude: order.delivery.currentLat,
          longitude: order.delivery.currentLong,
        },
      },
      productSourceCoordinates: extractProductSourceCoordinates(order.items),
      payment: order.payment,
      totals: {
        itemsTotal,
        totalAmount: order.totalAmount,
        discountAmount: order.discountAmount,
        deliveryFee: order.delivery.fee,
        dicountCode: order.discount?.code || null,
      },
      orderPreference: order.orderPreference,
    };
  }

  async getOrdersByStatus(
    userId: string,
    status: OrderStatus,
    params: PaginationParams,
  ) {
    const orders = await this.ordersRepository.findOrderByStatus(
      userId,
      status,
      params,
    );

    return {
      data: orders.data.map((order) => ({
        ...order,
        items: order.items.map((item) => ({
          ...item,
          totalPrice: item.product.price * item.quantity,
        })),
        totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
        productSourceCoordinates: extractProductSourceCoordinates(order.items),
      })),
      meta: orders.meta,
    };
  }

  async getCustomerOrdersPaginated(
    customerId: string,
    params: PaginationParams,
  ) {
    try {
      const orders = await this.ordersRepository.findByCustomer(
        customerId,
        params,
      );

      return {
        data: orders.data.map((order) => ({
          ...order,
          items: order.items.map((item) => ({
            ...item,
            totalPrice: item.product.price * item.quantity,
          })),
          totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
          productSourceCoordinates: extractProductSourceCoordinates(
            order.items,
          ),
        })),
        meta: orders.meta,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getDriverOrdersPaginated(driverId: string, params: PaginationParams) {
    const orders = await this.ordersRepository.findByDriver(driverId, params);

    return {
      data: orders.data.map((order) => ({
        ...order,
        items: order.items.map((item) => ({
          ...item,
          totalPrice: item.product.price * item.quantity,
        })),
        totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
        productSourceCoordinates: extractProductSourceCoordinates(order.items),
        delivery: {
          ...order.delivery,
          dropoffAddress: order.delivery.dropoffAddress,
          currentLocation: {
            latitude: order.delivery.currentLat,
            longitude: order.delivery.currentLong,
          },
        },
      })),
      meta: orders.meta,
    };
  }
  async getCurrentDriverOrders(userId: string, params: PaginationParams) {
    const driver = await this.driversRepository.findByUserId(userId);
    return this.getDriverOrdersPaginated(driver.id, params);
  }

  async getAllOrdersPaginated(
    params: PaginationDto & {
      status?: OrderStatus;
      customerId?: string;
      driverId?: string;
      startDate?: Date;
      endDate?: Date;
    },
  ) {
    try {
      const orders = await this.ordersRepository.findWithFilters(
        params,
        params.startDate,
        params.endDate,
        params.status,
        params.customerId,
      );
      return {
        data: orders.data.map((order) => ({
          ...order,
          items: order.items.map((item) => ({
            ...item,
            totalPrice: item.product.price * item.quantity,
          })),
          totalItems: order.items.reduce((sum, item) => sum + item.quantity, 0),
          productSourceCoordinates: extractProductSourceCoordinates(
            order.items,
          ),
          delivery: {
            ...order.delivery,
            dropoffAddress: order.delivery.dropoffAddress,
            currentLocation: {
              latitude: order.delivery.currentLat,
              longitude: order.delivery.currentLong,
            },
          },
        })),
        meta: orders.meta,
      };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getOrderStatistics(startDate?: Date, endDate?: Date) {
    const { totalOrders, ordersByStatus, revenue, ordersPerDay } =
      await this.ordersRepository.getOrderStats(startDate, endDate);
    return {
      totalOrders,
      ordersByStatus: ordersByStatus.reduce((acc, curr) => {
        acc[curr.status] = curr._count.id;
        return acc;
      }, {}),
      revenue,
      avgOrderValue: totalOrders > 0 ? revenue / totalOrders : 0,
      ordersPerDay,
      period: {
        start: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: endDate || new Date(),
      },
    };
  }

  async updateOrderStatus(
    orderId: string,
    status: OrderStatus,
    userId: string,
    userRole: UserRole,
    notes?: string,
  ) {
    const order = await this.ordersRepository.findOne(orderId);

    if (!order)
      throw new NotFoundException(`Order with ID ${orderId} not found`);

    if (userRole === UserRole.CUSTOMER) {
      if (order.customer.id !== userId) {
        throw new ForbiddenException(
          'You do not have permission to update this order',
        );
      }

      if (
        status !== OrderStatus.CANCELLED ||
        (order.status !== OrderStatus.PENDING &&
          order.status !== OrderStatus.PENDING_PAYMENT)
      ) {
        throw new ForbiddenException(
          'Customers can only cancel pending or pending payment orders',
        );
      }
    } else if (userRole === UserRole.DRIVER) {
      if (!order.delivery || order.delivery.currentDriver.user.id !== userId)
        throw new ForbiddenException(
          'You do not have permission to update this order',
        );
    }

    const updatedOrder = await this.ordersRepository.update(orderId, {
      status,
    });

    await this.notificationService.sendSingleNotification(
      order.customer.id,
      'Order Status Updated',
      `Your order #${orderId} has been ${status.toLowerCase()} ${notes ? `(${notes})` : ''}`,
      {
        type: 'ORDER_STATUS_UPDATE',
        orderId,
        status,
      },
    );

    return updatedOrder;
  }

  async cancelOrder(
    orderId: string,
    userId: string,
    userRole: UserRole,
    reason: string,
  ) {
    const order = await this.ordersRepository.findOne(orderId);

    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    if (order.status === OrderStatus.CANCELLED) {
      throw new BadRequestException('Order already cancelled');
    }

    if (order.status === OrderStatus.DELIVERED) {
      throw new BadRequestException('Order already delivered');
    }

    if (order.customer.id !== userId) {
      throw new ForbiddenException(
        'You do not have permission to cancel this order',
      );
    }

    if (order.isPayed) {
      throw new ForbiddenException('You can only cancel unpaid orders');
    }

    return this.updateOrderStatus(
      orderId,
      OrderStatus.CANCELLED,
      userId,
      userRole,
      reason,
    );
  }

  async removeOrder(id: string) {
    const order = await this.ordersRepository.findOne(id);

    if (order.status !== OrderStatus.DELIVERED) {
      throw new BadRequestException('Can only remove completed orders');
    }

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return this.ordersRepository.remove(id);
  }

  async getOrderTimeline(id: string) {
    const order = await this.ordersRepository.findOne(id);
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    const timeline = [];
    timeline.push({
      type: 'ORDER_CREATED',
      status: order.status,
      timestamp: order.createdAt,
      description: 'Order was created',
    });

    // Add delivery status changes
    if (order.delivery) {
      order.delivery.statusHistory.forEach((history) => {
        timeline.push({
          type: 'DELIVERY_STATUS',
          status: history.status,
          timestamp: history.createdAt,
          description:
            history.notes || `Delivery status changed to ${history.status}`,
        });
      });
    }

    if (order.payment) {
      timeline.push({
        type: 'PAYMENT',
        status: order.payment.status,
        timestamp: order.payment.createdAt,
        description: `Payment ${order.payment.status}`,
        amount: order.payment.amount,
        paymentId: order.payment.paymentId,
      });
    }
    timeline.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    return {
      orderId: id,
      timeline,
    };
  }

  async rateOrder(
    orderId: string,
    userId: string,
    rating: number,
    comment?: string,
  ) {
    const order = await this.ordersRepository.findOne(orderId);

    if (!order)
      throw new NotFoundException(`Order with ID ${orderId} not found`);

    if (order.customer.id !== userId)
      throw new ForbiddenException('You can only rate your own orders');

    if (order.status !== OrderStatus.DELIVERED)
      throw new ForbiddenException('You can only rate delivered orders');

    return this.reviewsRepository.create({
      rating,
      comment,
      driverDetails: { connect: { id: order.delivery.currentDriver.id } },
      reviewer: { connect: { id: userId } },
    });
  }

  async getDailyOrders(date?: string) {
    const orders = await this.ordersRepository.getDailyOrders(date);
    const totalOrders = orders.length;
    const totalAmount = orders.reduce(
      (sum, order) => sum + (order.totalAmount || 0),
      0,
    );

    const ordersByStatus = orders.reduce(
      (acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    // Calculate average order value
    const averageOrderValue = totalOrders > 0 ? totalAmount / totalOrders : 0;

    return {
      date: date || new Date().toISOString().split('T')[0],
      statistics: {
        totalOrders,
        totalAmount,
        averageOrderValue,
        ordersByStatus,
      },
      orders: orders.map((order) => ({
        id: order.id,
        createdAt: order.createdAt,
        status: order.status,
        totalAmount: order.totalAmount,
        customer: order.customer,
        itemCount: order.items.length,
        driver: order.delivery?.currentDriver?.user || null,
        dropoff: order.delivery?.dropoffAddress,
        lastStatus: order.delivery?.statusHistory[0],
      })),
    };
  }

  async createOrderIntent(dto: InitiateOrder, userId: string) {
    try {
      const customer = await this.usersRepository.findOne(userId);

      if (!customer) {
        throw new BadRequestException('User not found');
      }

      const dropoffAddress = await this.prisma.address.findUnique({
        where: { id: dto.addressId },
      });

      if (!dropoffAddress) {
        throw new BadRequestException('Invalid dropoff address');
      }

      const products = await this.validateProducts(dto.items);
      const productMap = new Map(products.map((p) => [p.id, p]));

      // Group items by location
      const itemsByLocation = this.groupItemsByLocation(dto.items, productMap);

      // Calculate fees
      const pickupLocations = this.getPickupLocations(itemsByLocation);
      const { totalDistance } = this.calculateDeliveryFees(
        pickupLocations,
        dropoffAddress,
      );

      // Calculate product total
      const productTotal = Array.from(itemsByLocation.values())
        .flatMap((loc) => loc.items)
        .reduce((sum, item) => sum + item.price, 0);

      // Calculate order total
      const orderTotal = productTotal;

      // Apply discounts
      // const discountData = await this.applyDiscounts(userId, orderTotal);
      // if (discountData) {
      //   orderTotal -= discountData.discountAmount;
      // }

      const order = await this.ordersRepository.create({
        status: OrderStatus.PENDING_PAYMENT,
        totalAmount: orderTotal,
        orderPreference: dto.orderPreference,
        customer: { connect: { id: userId } },
        isPayed: false,
        delivery: {
          create: {
            status: ErrandStatus.PENDING_PAYMENT,
            dropoffAddress: { connect: { id: dropoffAddress.id } },
            pickupAddresses: pickupLocations.map((loc) => loc.id),
            estimatedDistance: totalDistance,
            estimatedDuration: Math.round(totalDistance * 3 * 60),
          },
        },
      });

      this.notifyCustomer(customer.id, order.id);

      return order;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async retryOrderPayment(dto: RetryOrderDto) {
    try {
      const order = await this.ordersRepository.findOne(dto.orderId);

      if (!order) {
        throw new BadRequestException('Order not found');
      }

      if (order.status !== OrderStatus.PENDING_PAYMENT) {
        throw new BadRequestException('Order is not pending payment');
      }

      if (order.payment.status === 'SUCCESSFUL') {
        throw new BadRequestException('Order is already paid');
      }
      const driver = await this.driversRepository.findOne(dto.driverId);

      if (!driver) {
        throw new BadRequestException('Driver not found');
      }

      if (driver.currentStatus !== 'AVAILABLE') {
        throw new BadRequestException('Driver is not available');
      }

      const delivery = await this.deliveryRepository.findOne(order.id);
      if (!delivery.currentDriver) {
        await this.deliveryRepository.updateCurrentDriver(
          delivery.id,
          driver.id,
        );
      }

      const payment = await this.paymentRepository.findByOrderId(order.id);

      if (!payment) throw new BadRequestException('Payment not found');
      if (payment.status === 'SUCCESSFUL')
        throw new BadRequestException('Order is already paid');

      const res = await this.campayService.collectPayment({
        amount: order.totalAmount.toString(),
        from: payment.phoneNumber,
        description: `Order payment retry for ${order.customer.fullname}`,
        external_reference: `RETRY_${Date.now()}`,
        email: order.customer.email,
        order,
      });
      if (res.status === 200) {
        await this.paymentRepository.update(payment.id, {
          paymentId: res.data.reference,
          status: 'PENDING',
          transactionUUID: res.data.reference,
        });
      } else {
        await this.prisma.order.update({
          where: { id: order.id },
          data: { status: OrderStatus.CANCELLED },
        });
      }
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
