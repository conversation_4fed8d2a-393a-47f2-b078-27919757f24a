import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { OrderPaymentDto } from './dto/order-payment.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { User } from 'src/common/decorators/user.decorator';
import { IsPhoneVerifiedGuard } from 'src/common/guards/isPhoneVerified.guard';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RetryPaymentDto } from 'src/core/campay/dto/create.dto';
@ApiTags('payments')
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('initiate')
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  @ApiOperation({ summary: 'Request a payment' })
  @ApiBody({ type: OrderPaymentDto })
  @ApiResponse({ status: 200, description: 'Payment request response' })
  @ApiBearerAuth()
  async requestPayment(@Body() body: OrderPaymentDto, @User() user) {
    return this.paymentsService.requestPayment(body, user.userId);
  }

  // get all users payment history
  @Get('history')
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  @ApiOperation({ summary: 'Get payment history' })
  @ApiBearerAuth()
  async getPaymentHistory(@User() user) {
    return this.paymentsService.getPaymentHistory(user.userId);
  }

  @Post('retry')
  @ApiOperation({ summary: 'Retry a payment' })
  @ApiResponse({ status: 200, description: 'Payment retry response' })
  @ApiBody({ type: RetryPaymentDto })
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  async retryPayment(@Body() dto: RetryPaymentDto) {
    return this.paymentsService.retryPayment(dto.orderId);
  }

  @Post('status')
  @UseGuards(JwtAuthGuard, IsPhoneVerifiedGuard)
  @ApiOperation({ summary: 'Get transaction status' })
  @ApiResponse({ status: 200, description: 'Transaction status response' })
  async getTransactionStatus(@Body('paymentId') paymentId: string) {
    return this.paymentsService.getTransactionStatus(paymentId);
  }
}
