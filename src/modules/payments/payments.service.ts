import { Injectable } from '@nestjs/common';
import { CampayService } from 'src/core/campay/campay.service';
import { OrderPaymentDto } from './dto/order-payment.dto';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';

@Injectable()
export class PaymentsService {
  constructor(
    private readonly campayService: CampayService,
    private readonly prisma: PrismaService,
  ) {}
  async retryPayment(orderId: string) {
    return this.campayService.retryPayment(orderId);
  }

  async getTransactionStatus(paymentId: string) {
    return this.campayService.getTransactionStatus(paymentId);
  }

  async requestPayment(body: OrderPaymentDto, userId: string) {
    return this.campayService.initiatePayment(
      { orderId: body.orderId, from: body.phoneNumber },
      userId,
    );
  }

  async getPaymentHistory(userId: string) {
    try {
      const payments = await this.prisma.payment.findMany({
        where: { order: { customer: { id: userId } } },
        include: { order: true },
      });
      return payments;
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
