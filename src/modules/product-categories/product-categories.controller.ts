import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CreateProductCategoryDto } from './dto/create-product-category.dto';
import { UpdateProductCategoryDto } from './dto/update-product-category.dto';
import { ProductCategoryService } from './product-categories.service';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';

@ApiTags('product categories')
@Controller('product-categories')
export class ProductCategoryController {
  constructor(
    private readonly productCategoryService: ProductCategoryService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new product category' })
  @ApiResponse({
    status: 201,
    description: 'The product category has been successfully created.',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict: ProductCategory already exists.',
  })
  create(@Body() createProductCategoryDto: CreateProductCategoryDto) {
    return this.productCategoryService.create(createProductCategoryDto);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all product categories' })
  @ApiResponse({ status: 200, description: 'List of all product categories' })
  findAll() {
    return this.productCategoryService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Retrieve a product category by ID' })
  @ApiResponse({ status: 200, description: 'Product category found' })
  @ApiResponse({ status: 404, description: 'Product category not found' })
  findOne(@Param('id') id: string) {
    return this.productCategoryService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a product category' })
  @ApiResponse({
    status: 200,
    description: 'Product category successfully updated',
  })
  @ApiResponse({ status: 404, description: 'Product category not found' })
  update(
    @Param('id') id: string,
    @Body() updateProductCategoryDto: UpdateProductCategoryDto,
  ) {
    return this.productCategoryService.update(id, updateProductCategoryDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a product category' })
  @ApiResponse({
    status: 200,
    description: 'Product category successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'Product category not found' })
  remove(@Param('id') id: string) {
    return this.productCategoryService.remove(id);
  }
}
