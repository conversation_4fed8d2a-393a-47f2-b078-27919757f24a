import { ApiProperty } from '@nestjs/swagger';
import { ProductCategory } from 'src/common/schemas';

export class ProductCategoryEntity implements ProductCategory {
  createdAt: Date;
  updatedAt: Date;
  products: Product[];

  @ApiProperty({ description: 'The unique identifier of the category' })
  id: string;

  @ApiProperty({ description: 'Name of the product category' })
  name: string;
}
class Product {
  @ApiProperty({ description: 'The unique identifier of the product' })
  id: string;

  @ApiProperty({ description: 'Name of the product' })
  name: string;

  @ApiProperty({ description: 'Price of the product' })
  price: number;

  @ApiProperty({ description: 'Images of the product' })
  images: string[];
  @ApiProperty({ description: 'Description of the product' })
  description: string;
}
export class ProductCategoryResponse {
  @ApiProperty({ type: ProductCategoryEntity })
  data: ProductCategoryEntity;
}
