import { Module } from '@nestjs/common';
import { ProductCategoryService } from './product-categories.service';
import { ProductCategoryController } from './product-categories.controller';
import { ProductCategoryRepository } from 'src/common/repositories';

@Module({
  controllers: [ProductCategoryController],
  providers: [ProductCategoryService, ProductCategoryRepository],
})
export class ProductCategoriesModule {}
