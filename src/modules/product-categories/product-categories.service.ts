import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductCategoryDto } from './dto/create-product-category.dto';
import { UpdateProductCategoryDto } from './dto/update-product-category.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { ProductCategoryRepository } from 'src/common/repositories';

@Injectable()
export class ProductCategoryService {
  constructor(
    private readonly productCategoryRepository: ProductCategoryRepository,
  ) {}

  async create(createProductCategoryDto: CreateProductCategoryDto) {
    const { name } = createProductCategoryDto;
    try {
      const existingCategory =
        await this.productCategoryRepository.findByName(name);
      if (existingCategory) {
        throw new NotFoundException(
          `ProductCategory with name ${name} already exists`,
        );
      }
      return await this.productCategoryRepository.create(
        createProductCategoryDto,
      );
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(id: string, updateProductCategoryDto: UpdateProductCategoryDto) {
    const { name } = updateProductCategoryDto;

    try {
      const existingCategory =
        await this.productCategoryRepository.findByName(name);
      if (existingCategory) {
        throw new NotFoundException(
          `ProductCategory with name ${name} already exists`,
        );
      }
      const updatedCategory = await this.productCategoryRepository.update(
        id,
        updateProductCategoryDto,
      );

      return updatedCategory;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const category = await this.productCategoryRepository.findOne(id);

      if (!category)
        throw new NotFoundException(`ProductCategory with id ${id} not found`);

      return category;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findByName(name: string) {
    try {
      const category = await this.productCategoryRepository.findByName(name);

      if (!category)
        throw new NotFoundException(
          `ProductCategory with name ${name} not found`,
        );

      return category;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll() {
    try {
      return this.productCategoryRepository.findAll();
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string) {
    try {
      await this.findOne(id);
      await this.productCategoryRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
