import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { CreateProductSourceCategoryDto } from './dto/create-product-source-category.dto';
import { UpdateProductSourceCategoryDto } from './dto/update-product-source-category.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ProductSourceCategoryService } from './product-source-categories.service';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { UseGuards } from '@nestjs/common';

@ApiTags('product source categories')
@Controller('product-source-categories')
export class ProductSourceCategoryController {
  constructor(
    private readonly productSourceCategoryService: ProductSourceCategoryService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new product source category' })
  @ApiResponse({
    status: 201,
    description: 'The product source category has been successfully created.',
  })
  @ApiBearerAuth()
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: CreateProductSourceCategoryDto })
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  async create(
    @Body() createProductSourceCategoryDto: CreateProductSourceCategoryDto,
  ) {
    return this.productSourceCategoryService.create(
      createProductSourceCategoryDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all product source categories' })
  @ApiResponse({
    status: 200,
    description: 'List of product source categories',
  })
  async findAll() {
    return this.productSourceCategoryService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product source category by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the product source category to retrieve',
  })
  @ApiResponse({ status: 200, description: 'The product source category' })
  @ApiResponse({ status: 404, description: 'ProductSourceCategory not found' })
  async findOne(@Param('id') id: string) {
    return this.productSourceCategoryService.findOne(id);
  }
  @Get('name/:name')
  @ApiOperation({ summary: 'Get a product source category by name' })
  @ApiParam({
    name: 'name',
    type: String,
    description: 'Name of the product source category to retrieve',
  })
  @ApiResponse({ status: 200, description: 'The product source category' })
  @ApiResponse({ status: 404, description: 'ProductSourceCategory not found' })
  async findByName(@Param('name') name: string) {
    return this.productSourceCategoryService.findByName(name);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a product source category by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the product source category to update',
  })
  @ApiResponse({
    status: 200,
    description: 'The updated product source category',
  })
  @ApiResponse({ status: 404, description: 'ProductSourceCategory not found' })
  @ApiBody({ type: UpdateProductSourceCategoryDto })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  async update(
    @Param('id') id: string,
    @Body() updateProductSourceCategoryDto: UpdateProductSourceCategoryDto,
  ) {
    return this.productSourceCategoryService.update(
      id,
      updateProductSourceCategoryDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a product source category by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the product source category to delete',
  })
  @ApiResponse({
    status: 204,
    description: 'Product source category successfully deleted',
  })
  @ApiResponse({ status: 404, description: 'ProductSourceCategory not found' })
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBearerAuth()
  async remove(@Param('id') id: string): Promise<void> {
    return this.productSourceCategoryService.remove(id);
  }
}
