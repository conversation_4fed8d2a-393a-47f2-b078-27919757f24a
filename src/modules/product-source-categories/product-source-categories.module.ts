import { Module } from '@nestjs/common';
import { ProductSourceCategoryService } from './product-source-categories.service';
import { ProductSourceCategoryController } from './product-source-categories.controller';
import { SourceCategoryRepository } from 'src/common/repositories';

@Module({
  controllers: [ProductSourceCategoryController],
  providers: [ProductSourceCategoryService, SourceCategoryRepository],
})
export class ProductSourceCategoriesModule {}
