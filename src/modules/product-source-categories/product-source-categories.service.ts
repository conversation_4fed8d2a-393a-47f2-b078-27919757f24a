import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductSourceCategoryDto } from './dto/create-product-source-category.dto';
import { UpdateProductSourceCategoryDto } from './dto/update-product-source-category.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { SourceCategoryRepository } from 'src/common/repositories';

@Injectable()
export class ProductSourceCategoryService {
  constructor(
    private readonly productSourceCategory: SourceCategoryRepository,
  ) {}

  async create(createProductSourceCategoryDto: CreateProductSourceCategoryDto) {
    try {
      const existingCategory = await this.productSourceCategory.findByName(
        createProductSourceCategoryDto.name,
      );
      if (existingCategory) {
        throw new NotFoundException(
          `ProductSourceCategory with name ${createProductSourceCategoryDto.name} already exists`,
        );
      }
      return await this.productSourceCategory.create(
        createProductSourceCategoryDto,
      );
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll() {
    try {
      return this.productSourceCategory.findAll();
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findByName(name: string) {
    try {
      const category = await this.productSourceCategory.findByName(name);

      if (!category)
        throw new NotFoundException(
          `ProductSourceCategory with name ${name} not found`,
        );

      return category;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const category = await this.productSourceCategory.findOne(id);

      if (!category)
        throw new NotFoundException(
          `ProductSourceCategory with id ${id} not found`,
        );

      return category;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(
    id: string,
    updateProductSourceCategoryDto: UpdateProductSourceCategoryDto,
  ) {
    try {
      const category = await this.productSourceCategory.findOne(id);
      if (!category)
        throw new NotFoundException(
          `ProductSourceCategory with id ${id} not found`,
        );
      return await this.productSourceCategory.update(
        id,
        updateProductSourceCategoryDto,
      );
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string): Promise<void> {
    try {
      const category = await this.productSourceCategory.findOne(id);
      if (!category)
        throw new NotFoundException(
          `ProductSourceCategory with id ${id} not found`,
        );
      return await this.productSourceCategory.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
