import { ApiProperty } from '@nestjs/swagger';

export class ProductSourceCategory {
  @ApiProperty({ description: 'The unique identifier of the category' })
  id: string;

  @ApiProperty({ description: 'Name of the source category' })
  name: string;

  @ApiProperty({ description: 'Logo URL of the source category' })
  logo: string;

  @ApiProperty({ description: 'Array of product sources in this category' })
  sources: ProductSource[];
}

class ProductSource {
  @ApiProperty({ description: 'The unique identifier of the product source' })
  id: string;

  @ApiProperty({ description: 'Name of the product source' })
  name: string;

  @ApiProperty({ description: 'Google Place ID of the product source' })
  placeId: string;

  @ApiProperty({ description: 'Rating of the product source' })
  rating: number;

  @ApiProperty({ description: 'Array of image URLs for the product source' })
  images: string[];
}
