import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateProductSourceDto {
  @ApiProperty({ description: 'The name of the restaurant or product source.' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'The total rating of the product source.' })
  @IsString()
  @IsNotEmpty()
  ratings: string;

  @ApiProperty({
    description: 'The Google Place ID of the product source, if available.',
  })
  @IsString()
  @IsNotEmpty()
  placeId: string;

  @ApiProperty({
    description: 'The latitude coordinate of the product source.',
  })
  @IsString()
  @IsNotEmpty()
  lat: string;

  @ApiProperty({
    description: 'The longitude coordinate of the product source.',
  })
  @IsString()
  @IsNotEmpty()
  long: string;

  @ApiProperty({
    description: 'Street address of the product source.',
  })
  @IsString()
  @IsOptional()
  street: string;

  @ApiProperty({
    description: 'City of the product source.',
  })
  @IsString()
  @IsOptional()
  city: string;

  @ApiProperty({
    description: 'State of the product source.',
  })
  @IsString()
  @IsOptional()
  state: string;

  @ApiProperty({
    description: 'Country of the product source.',
  })
  @IsString()
  @IsOptional()
  country: string;

  @ApiProperty({
    description: 'Radius of operation for the product source.',
  })
  @IsString()
  @IsOptional()
  radius: string;

  @ApiProperty({
    description: 'A detailed description of the product source.',
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'total product source rating',
  })
  @IsString()
  @IsNotEmpty()
  totalRating: string;

  @ApiProperty({
    type: 'array',
    items: {
      type: 'string',
      format: 'binary',
    },
    description: 'Product source images (jpg, jpeg, png, gif) - max 4 files',
    required: false,
  })
  @IsOptional()
  images: any[];
}
