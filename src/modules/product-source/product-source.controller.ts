import { JwtAuthGuard } from './../../common/guards/jwt-auth.guard';
import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  UseInterceptors,
  UploadedFiles,
  Query,
  ParseArrayPipe,
  HttpStatus,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBasicAuth,
} from '@nestjs/swagger';
import { ProductSourceService } from './product-source.service';
import { CreateProductSourceDto } from './dto/create-product-source.dto';
import { UpdateProductSourceDto } from './dto/update-product-source.dto';
import { UploadImagesDto } from './dto/upload-images.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ProductSource } from './product-source.entity';
import { SearchProductSourceDto } from './dto/search-product.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';

@ApiTags('product sources')
@Controller('product-sources')
export class ProductSourceController {
  constructor(private readonly productSourceService: ProductSourceService) {}

  @Post()
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Create a new Product source',
    description: 'Creates a new product source with basic information.',
  })
  @ApiBody({
    description: 'Product Source creation data',
    type: CreateProductSourceDto,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Product source successfully created',
    type: ProductSource,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBasicAuth()
  @UseInterceptors(FilesInterceptor('images', 4))
  async create(
    @Body() createProductSourceDto: CreateProductSourceDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.productSourceService.create(createProductSourceDto, files);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all product sources',
    description: 'Retrieves a paginated list of all product sources',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of product sources',
    type: [ProductSource],
  })
  async findAll(@Query() pagination: PaginationDto) {
    return this.productSourceService.findAll(pagination);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Create multiple product sources',
    description: 'Creates multiple product sources in a single request',
  })
  @ApiBody({
    description: 'Array of product sources to create',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Product sources successfully created',
    type: [ProductSource],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async createBulk(@Body() productSources: CreateProductSourceDto[]) {
    return this.productSourceService.createBulk(productSources);
  }
  @Delete('bulk')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Bulk delete product sources',
    description: 'Deletes multiple product sources and their associated images',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product sources successfully deleted',
  })
  async bulkDelete(
    @Body('ids', new ParseArrayPipe({ items: String })) ids: string[],
  ) {
    await this.productSourceService.bulkDelete(ids);
    return { message: 'Product sources successfully deleted' };
  }

  @Get('search')
  @ApiOperation({
    summary: 'Search product sources',
    description: 'Search product sources by name, location, and rating',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results',
    type: [ProductSource],
  })
  async search(@Query() searchDto: SearchProductSourceDto) {
    return this.productSourceService.search(searchDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get product source by ID',
    description:
      'Retrieves detailed information about a specific product source',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product source found',
    type: ProductSource,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Product source not found',
  })
  async findOne(@Param('id') id: string) {
    return this.productSourceService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Update product source',
    description: 'Updates basic information of a product source',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiBody({
    description: 'Update product source data',
    type: UpdateProductSourceDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product source successfully updated',
    type: ProductSource,
  })
  @UseInterceptors(FilesInterceptor('images', 4))
  async update(
    @Param('id') id: string,
    @Body() updateProductSourceDto: UpdateProductSourceDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.productSourceService.updateProductSource(
      id,
      updateProductSourceDto,
      files,
    );
  }
  @Delete(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Delete product source',
    description: 'Deletes a product source and its associated images',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product source successfully deleted',
  })
  async remove(@Param('id') id: string) {
    await this.productSourceService.remove(id);
    return { message: 'Product source successfully deleted' };
  }
  @Post('upload-images/:id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  @ApiOperation({
    summary: 'Upload images for a product source',
    description: 'Upload up to 4 images for an existing product source',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload images for product source',
    type: UploadImagesDto,
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Images successfully uploaded',
    type: ProductSource,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file format or too many files',
  })
  @UseInterceptors(FilesInterceptor('images', 4))
  async uploadImages(
    @Param('id') id: string,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    if (!files || files.length === 0)
      throw new BadRequestException('No files uploaded');
    return this.productSourceService.handleImageUpload(id, files);
  }

  @Patch(':id/rating')
  @ApiOperation({
    summary: 'Update product source rating',
    description: 'Updates only the rating of a product source',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Rating successfully updated',
    type: ProductSource,
  })
  async updateRating(@Param('id') id: string, @Body('rating') rating: number) {
    return this.productSourceService.updateRating(id, rating);
  }

  @Delete(':id/images')
  @ApiOperation({
    summary: 'Delete all images from product source',
    description: 'Removes all images associated with a product source',
  })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'Product source ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Images successfully deleted',
    type: ProductSource,
  })
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiBasicAuth()
  async deleteAllImages(@Param('id') id: string) {
    return this.productSourceService.deleteAllImages(id);
  }
}
