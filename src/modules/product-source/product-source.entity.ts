import { ApiProperty } from '@nestjs/swagger';
import { CreateAddressDto } from 'src/modules/address/dto/create-address.dto';

export class ProductSource {
  @ApiProperty({ description: 'The unique identifier of the item' })
  id: string;

  @ApiProperty({ description: 'Name of the restaurant or product' })
  name: string;
  @ApiProperty({ description: 'Place Id of the restaurant or product' })
  placeId: string;

  @ApiProperty({ description: 'Rating of the restaurant or product' })
  rating: number;

  @ApiProperty({ description: 'Address of the restaurant or location' })
  address: CreateAddressDto;

  @ApiProperty({ description: 'Array of image URLs uploaded to Cloudinary' })
  images: string[];

  @ApiProperty({ description: 'Timestamp of when the item was created' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of when the item was last updated' })
  updatedAt: Date;
}
