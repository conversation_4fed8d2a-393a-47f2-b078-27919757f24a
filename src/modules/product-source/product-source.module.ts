import { Module } from '@nestjs/common';
import { ProductSourceService } from './product-source.service';
import { ProductSourceController } from './product-source.controller';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { ProductSourceRepository } from 'src/common/repositories';

@Module({
  controllers: [ProductSourceController],
  providers: [ProductSourceService, CloudinaryService, ProductSourceRepository],
})
export class ProductSourceModule {}
