import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { CreateProductSourceDto } from './dto/create-product-source.dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ProductSourceRepository } from 'src/common/repositories';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { UpdateProductSourceDto } from './dto/update-product-source.dto';
import { SearchProductSourceDto } from './dto/search-product.dto';

@Injectable()
export class ProductSourceService {
  private readonly MAX_IMAGES = 4;

  constructor(
    private readonly cloudinaryService: CloudinaryService,
    private readonly productSourceRepository: ProductSourceRepository,
  ) {}

  async create(
    createProductSourceDto: CreateProductSourceDto,
    files: Express.Multer.File[],
  ) {
    const findPlacesId = await this.productSourceRepository.findByPlacesId(
      createProductSourceDto.placeId,
    );
    if (findPlacesId) throw new BadRequestException('Place already exists');
    const images = [];
    if (files) {
      const uploadedFiles = await this.cloudinaryService.uploadFiles(
        files,
        'product-source',
      );
      const imageUrls = uploadedFiles.map((file) => file.secure_url);
      images.push(...imageUrls);
    }

    if (createProductSourceDto.images) {
      images.push(...createProductSourceDto.images);
    }

    try {
      return await this.productSourceRepository.create({
        ...createProductSourceDto,
        images,
        rating: parseFloat(createProductSourceDto.ratings),
        totalRating: parseFloat(createProductSourceDto.totalRating),
        lat: +createProductSourceDto.lat,
        long: +createProductSourceDto.long,
        radius: +createProductSourceDto.radius,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }
  async findAll(pagination: PaginationDto) {
    try {
      return this.productSourceRepository.findAll(pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async createBulk(createProductSourceDtos: CreateProductSourceDto[]) {
    try {
      const existingPlaces = await this.productSourceRepository.findByPlacesIds(
        createProductSourceDtos.map((dto) => dto.placeId),
      );

      if (existingPlaces.length > 0) {
        const duplicatePlaceIds = existingPlaces
          .map((place) => place.placeId)
          .join(', ');
        throw new BadRequestException(
          `Places already exist: ${duplicatePlaceIds}`,
        );
      }
      const sources = await this.productSourceRepository.createBulk(
        createProductSourceDtos.map((dto) => ({
          ...dto,
          rating: parseFloat(dto.ratings),
          totalRating: parseFloat(dto.totalRating),
          lat: +dto.lat,
          long: +dto.long,
          radius: +dto.radius,
        })),
      );

      return sources;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async handleImageUpload(id: string, files: Express.Multer.File[]) {
    if (files.length > this.MAX_IMAGES) {
      throw new BadRequestException(
        `Maximum ${this.MAX_IMAGES} images allowed`,
      );
    }
    try {
      const uploadedFiles = await this.cloudinaryService.uploadFiles(
        files,
        'product-source',
      );
      const imageUrls = uploadedFiles.map((file) => file.secure_url);
      return await this.productSourceRepository.update(id, {
        images: imageUrls,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async bulkDelete(ids: string[]) {
    try {
      return this.productSourceRepository.bulkDelete(ids);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async updateProductSource(
    id: string,
    updateProductSourceDto: UpdateProductSourceDto,
    files: Express.Multer.File[],
  ) {
    try {
      const productSource = await this.productSourceRepository.findOne(id);
      if (!productSource)
        throw new BadRequestException('Product source not found');

      const images = [];
      if (files) {
        const uploadedFiles = await this.cloudinaryService.uploadFiles(
          files,
          'product-source',
        );
        const imageUrls = uploadedFiles.map((file) => file.secure_url);
        images.push(...imageUrls);
        await this.deleteImagesFromCloudinary(productSource.images);
      }

      if (updateProductSourceDto.images) {
        images.push(...updateProductSourceDto.images);
      }

      return await this.productSourceRepository.update(id, {
        ...updateProductSourceDto,
        images,
        rating: parseFloat(updateProductSourceDto.ratings),
        totalRating: parseFloat(updateProductSourceDto.totalRating),
        lat: +updateProductSourceDto.lat,
        long: +updateProductSourceDto.long,
        radius: +updateProductSourceDto.radius,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string) {
    try {
      const productSource = await this.productSourceRepository.findOne(id);
      if (!productSource)
        throw new BadRequestException('Product source not found');

      await this.deleteImagesFromCloudinary(productSource.images);
      return await this.productSourceRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  findOne(id: string) {
    try {
      const productSource = this.productSourceRepository.findOne(id);
      if (!productSource)
        throw new BadRequestException('Product source not found');
      return productSource;
    } catch (error) {
      handlePrismaError(error);
    }
  }
  async updateRating(id: string, rating: number) {
    try {
      const productSource = await this.productSourceRepository.findOne(id);
      if (!productSource)
        throw new BadRequestException('Product source not found');
      const totalRating = productSource.totalRating + rating;
      const ratings = productSource.rating + 1;
      return await this.productSourceRepository.update(id, {
        totalRating,
        rating: totalRating / ratings,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async deleteAllImages(id: string) {
    try {
      const productSource = await this.productSourceRepository.findOne(id);
      if (!productSource)
        throw new BadRequestException('Product source not found');
      await this.deleteImagesFromCloudinary(productSource.images);
      return await this.productSourceRepository.update(id, {
        images: [],
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async search(searchDto: SearchProductSourceDto) {
    try {
      return this.productSourceRepository.search(searchDto);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  private async deleteImagesFromCloudinary(images: any[]) {
    if (!images?.length) return;
    const publicIds = images
      .map((image) => {
        const publicId = this.cloudinaryService.checkPublicid(image);
        return publicId;
      })
      .filter((publicId) => publicId !== null); // Filter out null values

    if (publicIds.length > 0) {
      try {
        await this.cloudinaryService.deleteManyImages(publicIds);
        console.log(
          `Successfully deleted ${publicIds.length} product source images`,
        );
      } catch (error) {
        console.error('Failed to delete some product source images:', error);
      }
    }
  }
}
