import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class CreateProductDto {
  @ApiProperty({
    description: 'The name of the product',
    example: 'Updated Product',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'The description of the product',
    example: 'An updated description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'The price of the product',
    example: 89.99,
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  price: string;

  @ApiProperty({
    description: 'The category ID of the product',
    example: '60d0fe4f5311236168a109ca',
    required: false,
  })
  @IsOptional()
  @IsString()
  categoryId: string;

  @ApiProperty({
    description: 'The prosuct Souce ID of the product',
    example: '60d0fe4f5311236168a109cb',
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  productSourceId: string;

  @ApiProperty({
    description: 'deliverPrice',
    example: 6.999,
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  deliveryPrice: string;

  // @ApiProperty({ type: 'array', items: { type: 'string', format: 'binary' } })
  images: any[];
}
