import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseInterceptors,
  UploadedFiles,
  Patch,
  HttpStatus,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiConsumes,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { AddImagesDto } from './dto/add-images.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { Product } from './product.entity';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { PaginationDto } from 'src/common/dto/pagination.dto';

@ApiTags('Products')
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Create a new product with images' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Create product with images',
    type: CreateProductDto,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Product created successfully',
    type: Product,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or image upload failed',
  })
  @UseInterceptors(FilesInterceptor('images'))
  async createProduct(
    @Body() createProductDto: CreateProductDto,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<Product> {
    return this.productsService.create(createProductDto, files);
  }

  @Get()
  @ApiOperation({ summary: 'Retrieve all products' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all products',
    type: [Product],
  })
  async getAllProducts(@Query() pagination: PaginationDto): Promise<any> {
    return this.productsService.findAllProduct(pagination);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Retrieve a product by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Product UUID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product found',
    type: Product,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Product not found',
  })
  async getProductById(@Param('id') id: string): Promise<Product> {
    return this.productsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Update a product and optionally its images' })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    type: String,
  })
  @ApiBody({ type: CreateProductDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Product updated successfully',
    type: Product,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Product not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or image upload failed',
  })
  @UseInterceptors(FilesInterceptor('images'))
  async updateProduct(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @UploadedFiles() images: Express.Multer.File[],
  ): Promise<Product> {
    return this.productsService.update(id, updateProductDto, images);
  }

  @Get('category/:categoryId')
  @ApiOperation({ summary: 'Retrieve products by category' })
  @ApiParam({
    name: 'categoryId',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of products in the specified category',
    type: [Product],
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found',
  })
  async getProductsByCategory(
    @Param('categoryId') categoryId: string,
    @Query() pagination: PaginationDto,
  ) {
    return this.productsService.getByCategory(categoryId, pagination);
  }

  @Get('category/:sourceCategoryId')
  @ApiOperation({ summary: 'Retrieve products by store' })
  @ApiParam({
    name: 'sourceCategoryId',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of products in the specified store',
    type: [Product],
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Store not found',
  })
  async getProductsByStore(
    @Param('sourceCategoryId') sourceId: string,
    @Query() pagination: PaginationDto,
  ) {
    return this.productsService.getBySourceCategory(sourceId, pagination);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Delete a product' })
  @ApiParam({ name: 'id', type: String })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Product deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Product not found',
  })
  async deleteProduct(@Param('id') id: string): Promise<void> {
    await this.productsService.remove(id);
  }

  @Post(':id/images')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Add images to an existing product' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Add images to product',
    type: AddImagesDto,
  })
  @ApiParam({
    name: 'id',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Images added successfully',
    type: Product,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Product not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Image upload failed',
  })
  @UseInterceptors(FilesInterceptor('images'))
  async addProductImages(
    @Param('id') id: string,
    @UploadedFiles() files: Express.Multer.File[],
  ): Promise<Product> {
    return this.productsService.addProductImages(id, files);
  }

  @Get('source/:id')
  async getProductbySource(@Param('id') id: string) {
    return this.productsService.getProductByProductSource(id);
  }

  @Post('bulk')
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @ApiOperation({ summary: 'Create multiple products' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        products: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              description: { type: 'string' },
              price: { type: 'string' },
              productSourceId: { type: 'string' },
              deliveryPrice: { type: 'string' },
              images: {
                type: 'array',
                items: { type: 'string' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Products created successfully',
    type: [Product],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async createBulk(@Body() products: CreateProductDto[]): Promise<Product[]> {
    return this.productsService.createBulk(products);
  }
}
