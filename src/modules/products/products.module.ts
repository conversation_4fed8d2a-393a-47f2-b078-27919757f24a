import { Module } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import {
  ProductCategoryRepository,
  ProductRepository,
  SourceCategoryRepository,
} from 'src/common/repositories';

@Module({
  controllers: [ProductsController],
  providers: [
    ProductsService,
    CloudinaryService,
    ProductCategoryRepository,
    ProductRepository,
    SourceCategoryRepository,
  ],
})
export class ProductsModule {}
