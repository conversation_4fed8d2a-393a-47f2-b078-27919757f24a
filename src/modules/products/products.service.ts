import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import {
  ProductCategoryRepository,
  ProductRepository,
  SourceCategoryRepository,
} from 'src/common/repositories';

@Injectable()
export class ProductsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly cloudinaryService: CloudinaryService,
    private readonly productRepository: ProductRepository,
    private readonly sourceCategoryRepository: SourceCategoryRepository,
    private readonly productCategoryRepository: ProductCategoryRepository,
  ) {}

  // Image handling methods
  private async uploadImagesToCloudinary(files: Express.Multer.File[]) {
    if (!files?.length) return [];

    return await Promise.all(
      files.map((image) =>
        this.cloudinaryService.uploadFile(image, 'products'),
      ),
    );
  }

  private async deleteImagesFromCloudinary(images: any[]) {
    if (!images?.length) return;
    const publicIds = images
      .map((image) => {
        const publicId = this.cloudinaryService.checkPublicid(image.url);
        return publicId;
      })
      .filter((publicId) => publicId !== null); // Filter out null values

    if (publicIds.length > 0) {
      try {
        await this.cloudinaryService.deleteManyImages(publicIds);
        console.log(`Successfully deleted ${publicIds.length} product images`);
      } catch (error) {
        console.error('Failed to delete some product images:', error);
      }
    }
  }

  private async validateReferences(productSourceId: string): Promise<void> {
    const [sourceCategory] = await Promise.all([
      this.prisma.productSource.findUnique({ where: { id: productSourceId } }),
    ]);

    if (!sourceCategory) {
      throw new NotFoundException(
        `ProductSourceCategory with id ${productSourceId} not found`,
      );
    }
  }

  private async validateProduct(id: string) {
    const product = await this.productRepository.findOne(id);
    if (!product) {
      throw new NotFoundException(`Product with id ${id} not found`);
    }

    return product;
  }

  async create(
    createProductDto: CreateProductDto,
    files: Express.Multer.File[],
  ) {
    const {
      name,
      categoryId,
      deliveryPrice,
      productSourceId,
      description,
      price,
      images,
    } = createProductDto;
    const productImages = [];
    try {
      await this.validateReferences(productSourceId);
      const uploadedImages = await this.uploadImagesToCloudinary(files);
      productImages.push(...uploadedImages.map((image) => image.secure_url));
      if (images) {
        productImages.push(...images);
      }

      const product = await this.productRepository.create({
        name,
        description,
        price: +price,
        deliveryPrice: +deliveryPrice,
        productSource: { connect: { id: productSourceId } },
        ...(categoryId && { category: { connect: { id: categoryId } } }),
        images: productImages,
      });
      return product;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(
    id: string,
    updateProductDto: UpdateProductDto,
    newImages?: Express.Multer.File[],
  ) {
    try {
      const {
        categoryId,
        price,
        deliveryPrice,
        name,
        productSourceId,
        description,
        images,
      } = updateProductDto;
      const existingProduct = await this.validateProduct(id);

      if (updateProductDto.productSourceId || updateProductDto.categoryId) {
        await this.validateReferences(
          updateProductDto.productSourceId || existingProduct.productSource.id,
        );
      }

      const productImages = [];
      if (newImages?.length) {
        await this.deleteImagesFromCloudinary(existingProduct.images);
        const uploadedImages = await this.uploadImagesToCloudinary(newImages);
        productImages.push(...uploadedImages.map((image) => image.secure_url));
      }
      if (images) {
        productImages.push(...images);
      }

      return await this.productRepository.update(id, {
        name,
        description,
        price: +price,
        deliveryPrice: +deliveryPrice,
        productSource: { connect: { id: productSourceId } },
        ...(categoryId && { category: { connect: { id: categoryId } } }),
        images: productImages,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const product = await this.productRepository.findOne(id);

      if (!product) {
        throw new NotFoundException(`Product with id ${id} not found`);
      }
      return product;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAllProduct(pagination: PaginationDto) {
    try {
      return this.productRepository.findAll(pagination);
    } catch (error) {
      console.log(error);
      handlePrismaError(error);
    }
  }

  async getByCategory(categoryId: string, pagination: PaginationDto) {
    try {
      const category = await this.productCategoryRepository.findOne(categoryId);
      if (!category)
        throw new NotFoundException(
          `Product Category with id ${categoryId} not found`,
        );
      return this.productRepository.getByCategory(categoryId, pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getBySourceCategory(
    sourceCategoryId: string,
    pagination: PaginationDto,
  ) {
    try {
      const sourceCategory =
        await this.sourceCategoryRepository.findOne(sourceCategoryId);

      if (!sourceCategory)
        throw new NotFoundException(
          `ProductSourceCategory with id ${sourceCategoryId} not found`,
        );

      return this.productRepository.getBySourceCategory(
        sourceCategoryId,
        pagination,
      );
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(id: string) {
    try {
      const product = await this.validateProduct(id);

      await this.deleteImagesFromCloudinary(product.images);
      return await this.productRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async addProductImages(productId: string, files: Express.Multer.File[]) {
    try {
      await this.validateProduct(productId);
      const uploadedImages = await this.uploadImagesToCloudinary(files);

      const updated = await this.productRepository.update(productId, {
        images: uploadedImages.map((image) => image.secure_url),
      });
      return updated;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getProductByProductSource(sourceId: string) {
    try {
      const product = await this.prisma.product.findMany({
        where: { productSourceId: sourceId },
      });
      return product;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async createBulk(createProductDtos: CreateProductDto[]) {
    try {
      const createdProducts = await Promise.all(
        createProductDtos.map(async (dto) => {
          const {
            name,
            description,
            productSourceId,
            deliveryPrice,
            price,
            images,
          } = dto;

          await this.validateReferences(productSourceId);

          return await this.productRepository.create({
            name,
            description,
            price: +price,
            deliveryPrice: +deliveryPrice,
            productSource: { connect: { id: productSourceId } },
            images,
          });
        }),
      );

      return createdProducts;
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
