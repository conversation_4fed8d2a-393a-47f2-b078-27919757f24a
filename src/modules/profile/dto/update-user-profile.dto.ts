import { ApiProperty } from '@nestjs/swagger';
import { Gender } from '@prisma/client';
import { IsString, IsOptional, IsEnum } from 'class-validator';

export class UpdateUserProfileDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  fullname?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({ enum: Gender, required: false })
  @IsEnum(Gender)
  @IsOptional()
  gender?: Gender;

  @ApiProperty({ required: false })
  // @IsPhoneNumber()
  @IsOptional()
  phone?: string;
  @ApiProperty({
    required: false,
    description:
      'Date of birth in ISO-8601 format (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ). Leave empty or omit to keep current value.',
    example: '1990-01-15',
  })
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  image?: any;
}
