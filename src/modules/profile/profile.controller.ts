import {
  Controller,
  Patch,
  Get,
  Body,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  HttpCode,
  HttpStatus,
  Delete,
  Post,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiConsumes,
} from '@nestjs/swagger';
import { ProfileService } from './profile.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';
import { UploadImageDto } from './dto/upload-image.dto';
import { User } from 'src/common/decorators/user.decorator';
import { ProfileResponseDto } from './profile.entity';
import { UsersService } from 'src/modules/users/users.service';
import { AtLeastOneVerificationGuard } from 'src/common/guards/atLeastOneVerification.guard';
import { TokenDto } from '../users/dto/token.dto';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
@ApiTags('User Profiles')
@Controller('profile')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, AtLeastOneVerificationGuard)
export class ProfileController {
  constructor(
    private readonly profileService: ProfileService,
    private readonly usersService: UsersService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  @Patch()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully.',
    type: ProfileResponseDto,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Update user profile with optional image',
    type: UpdateUserProfileDto,
  })
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
      fileFilter: (req, file, cb) => {
        if (!file) {
          cb(null, true);
          return;
        }
        if (!file.mimetype.match(/^image\/(jpg|jpeg|png|gif)$/)) {
          cb(new BadRequestException('Only image files are allowed!'), false);
          return;
        }
        cb(null, true);
      },
    }),
  )
  async updateProfile(
    @User() user,
    @Body() updateProfileDto: UpdateUserProfileDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.profileService.updateUserProfile(
      user.userId,
      updateProfileDto,
      file,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get a user profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile retrieved successfully.',
    type: ProfileResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Profile not found.' })
  async getProfile(@User() user) {
    return this.profileService.getUserProfile(user.userId);
  }

  @Patch('image')
  @UseInterceptors(
    FileInterceptor('image', {
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/^image\/(jpg|jpeg|png|gif)$/)) {
          cb(new BadRequestException('Only image files are allowed!'), false);
        }
        cb(null, true);
      },
    }),
  )
  @ApiOperation({ summary: 'Upload profile image' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Profile image upload',
    type: UploadImageDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Profile image uploaded successfully.',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid file or no file uploaded.',
  })
  @ApiResponse({ status: 404, description: 'Profile not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async uploadProfileImage(
    @User() user,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.profileService.updateUserProfileImage(user.userId, file);
  }

  @Post('delete-request')
  @ApiOperation({ summary: 'Request account deletion' })
  @ApiResponse({
    status: 200,
    description: 'Deletion request sent successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async requestAccountDeletion(@User() user) {
    return this.usersService.sendDeleteRequest(user.userId);
  }

  @Delete('delete-account')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete user account' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Account successfully deleted',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  async deleteAccount(@User() user, @Body() dto: TokenDto) {
    return this.usersService.softDeleteUser(user.userId, dto.token);
  }

  @Get('test-cloudinary')
  @ApiOperation({ summary: 'Test Cloudinary connection' })
  @ApiResponse({
    status: 200,
    description: 'Cloudinary connection test result',
  })
  async testCloudinary() {
    try {
      const isConnected = await this.cloudinaryService.testConnection();
      return {
        success: isConnected,
        message: isConnected
          ? 'Cloudinary connection successful'
          : 'Cloudinary connection failed',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Cloudinary connection test failed',
        error: error.message,
      };
    }
  }
}
