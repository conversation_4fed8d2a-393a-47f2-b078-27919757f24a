import { ApiProperty } from '@nestjs/swagger';
import { Gender, UserRole } from '@prisma/client';

class ImageDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  url: string;

  @ApiProperty()
  publicId: string;
}

class UserProfileDto {
  @ApiProperty()
  nickname?: string;

  @ApiProperty({ enum: Gender })
  gender?: Gender;

  @ApiProperty({ type: ImageDto })
  avatar?: ImageDto;
}

class UserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  fullname: string;

  @ApiProperty()
  phone?: string;

  @ApiProperty()
  isVerified: boolean;

  @ApiProperty({ enum: UserRole })
  role: UserRole;

  @ApiProperty({ type: UserProfileDto })
  profile: UserProfileDto;
}

export class ProfileResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  nickname?: string;

  @ApiProperty({ enum: Gender })
  gender?: Gender;

  @ApiProperty({ type: ImageDto })
  avatar?: ImageDto;

  @ApiProperty({ type: UserDto })
  user: UserDto;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
