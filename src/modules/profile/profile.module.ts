import { Module } from '@nestjs/common';
import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import {
  OrdersRepository,
  OtpRepository,
  ProfileRepository,
  UsersRepository,
} from 'src/common/repositories';
import { UsersService } from '../users/users.service';

@Module({
  controllers: [ProfileController],
  providers: [
    ProfileService,
    UsersRepository,
    ProfileRepository,
    CloudinaryService,
    OrdersRepository,
    OtpRepository,
    UsersService,
  ],
})
export class ProfileModule {}
