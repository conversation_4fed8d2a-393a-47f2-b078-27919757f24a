import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateReviewDto {
  @ApiProperty({ description: 'Rating for the driver' })
  @IsNotEmpty()
  @IsNumber()
  rating: number;

  @ApiProperty({
    description: 'Optional comment for the rating',
    required: false,
  })
  @IsOptional()
  @IsString()
  comment: string;

  @ApiProperty({
    description: 'ID of the driver being reviewed',
    example: '60d0fe4f5311236168a109ca',
  })
  @IsNotEmpty()
  @IsString()
  driverId: string;
}
