import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { User } from 'src/common/decorators/user.decorator';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';

@ApiTags('reviews')
@Controller('reviews')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new review' })
  @ApiResponse({ status: 201, description: 'Review created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createReviewDto: CreateReviewDto, @User() user) {
    return this.reviewsService.create(createReviewDto, user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all reviews' })
  @ApiResponse({ status: 200, description: 'Returns all reviews' })
  findAll(@Query() pagination: PaginationDto) {
    return this.reviewsService.findAll(pagination);
  }

  @Get('user/:id')
  @ApiOperation({ summary: 'Get reviews by user ID' })
  @ApiResponse({ status: 200, description: 'Returns user reviews' })
  @ApiResponse({ status: 404, description: 'User not found' })
  findByUser(@Param('id') userId: string, @Query() pagination: PaginationDto) {
    return this.reviewsService.findByUser(userId, pagination);
  }

  @Get('driver/:id')
  @ApiOperation({ summary: 'Get reviews by driver ID' })
  @ApiResponse({ status: 200, description: 'Returns driver reviews' })
  @ApiResponse({ status: 404, description: 'Driver not found' })
  findByDriver(
    @Param('id') driverId: string,
    @Query() pagination: PaginationDto,
  ) {
    return this.reviewsService.findByDriver(driverId, pagination);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get review by ID' })
  @ApiResponse({ status: 200, description: 'Returns a review' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  findOne(@Param('id') id: string) {
    return this.reviewsService.findOne(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete review by ID' })
  @ApiResponse({ status: 200, description: 'Review deleted successfully' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  remove(@Param('id') id: string) {
    return this.reviewsService.remove(id);
  }
}
