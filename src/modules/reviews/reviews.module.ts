import { Module } from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { ReviewsController } from './reviews.controller';
import {
  DriversRepository,
  ReviewsRepository,
  UsersRepository,
} from 'src/common/repositories';

@Module({
  controllers: [ReviewsController],
  providers: [
    ReviewsService,
    ReviewsRepository,
    UsersRepository,
    DriversRepository,
  ],
})
export class ReviewsModule {}
