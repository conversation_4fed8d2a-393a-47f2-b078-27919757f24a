import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateReviewDto } from './dto/create-review.dto';
import { DriversRepository, ReviewsRepository } from 'src/common/repositories';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { PaginationDto } from 'src/common/dto/pagination.dto';

@Injectable()
export class ReviewsService {
  constructor(
    private readonly reviewsRepository: ReviewsRepository,
    private readonly driverRepository: DriversRepository,
  ) {}
  async create(createReviewDto: CreateReviewDto, userId: string) {
    try {
      const driver = await this.driverRepository.findByUserId(
        createReviewDto.driverId,
      );
      if (!driver) throw new NotFoundException('Driver not found');
      return this.reviewsRepository.create({
        rating: createReviewDto.rating,
        comment: createReviewDto.comment,
        driverDetails: { connect: { id: driver.id } },
        reviewer: { connect: { id: userId } },
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }

  findAll(pagination: PaginationDto) {
    try {
      return this.reviewsRepository.findAll(pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  findOne(id: string) {
    try {
      const review = this.reviewsRepository.findOne(id);
      if (!review) throw new NotFoundException('Review not found');
      return review;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  findByUser(userId: string, pagination: PaginationDto) {
    try {
      return this.reviewsRepository.findByUser(userId, pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  findByDriver(driverId: string, pagination: PaginationDto) {
    try {
      return this.reviewsRepository.findByDriver(driverId, pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  remove(id: string) {
    try {
      return this.reviewsRepository.remove(id);
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
