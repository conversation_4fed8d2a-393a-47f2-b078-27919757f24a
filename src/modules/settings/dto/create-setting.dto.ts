import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

export class CreateSettingDto {
  @ApiProperty({
    description: 'Key for the setting',
    example: 'delivery_fee_base'
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Value for the setting',
    example: '1000'
  })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiProperty({
    description: 'Description of the setting',
    example: 'Base delivery fee in XAF'
  })
  @IsString()
  @IsOptional()
  description?: string;
}