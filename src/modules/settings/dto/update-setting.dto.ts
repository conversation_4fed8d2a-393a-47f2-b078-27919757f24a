import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UpdateSettingDto {
  @ApiProperty({
    description: 'New value for the setting',
    example: '1200'
  })
  @IsString()
  value: string;

  @ApiProperty({
    description: 'New description for the setting',
    example: 'Updated base delivery fee in XAF'
  })
  @IsString()
  @IsOptional()
  description?: string;
}