import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { SettingsRepository } from 'src/common/repositories';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';

@Injectable()
export class SettingsService {
  constructor(private settingRepository: SettingsRepository) {}

  async create(createSettingDto: CreateSettingDto) {
    try {
      return this.settingRepository.create(createSettingDto);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll() {
    try {
      return this.settingRepository.findAll();
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(key: string) {
    try {
      const setting = await this.settingRepository.findOne(key);

      if (!setting) {
        throw new NotFoundException(`Setting with key '${key}' not found`);
      }

      return setting;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(key: string, updateSettingDto: UpdateSettingDto) {
    try {
      const setting = await this.settingRepository.findOne(key);

      if (!setting) {
        throw new NotFoundException(`Setting with key '${key}' not found`);
      }

      return this.settingRepository.update(key, updateSettingDto);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async remove(key: string) {
    try {
      const setting = await this.settingRepository.findOne(key);

      if (!setting) {
        throw new NotFoundException(`Setting with key '${key}' not found`);
      }

      return this.settingRepository.remove(key);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getSettingValue(key: string): Promise<string | null> {
    try {
      const setting = await this.settingRepository.findOne(key);
      if (!setting) {
        throw new NotFoundException(`Setting with key '${key}' not found`);
      }
      return setting?.value || null;
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
