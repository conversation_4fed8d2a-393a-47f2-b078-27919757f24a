import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
  WsException,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { WebsocketGuard } from 'src/common/guards/ws-gaurd.guard';
import { SocketAuthMiddleware } from 'src/common/middleware/ws.middleware';
import { PrismaService } from 'src/core/prisma/prisma.service';
import { UserRole } from '@prisma/client';
import { TrackingService } from './tracking.service';
import { Interval } from '@nestjs/schedule';
interface LocationUpdate {
  lat: number;
  lng: number;
  speed?: number;
  heading?: number;
  timestamp: number;
}

interface DeliveryLocation {
  deliveryId: string;
  driverId: string;
  currentLocation: LocationUpdate;
  locationHistory: LocationUpdate[];
  updatedAt: Date;
  estimatedArrival?: Date;
}

@UseGuards(WebsocketGuard)
@WebSocketGateway({
  namespace: 'tracking-gateway',
  cors: {
    origin: ['*'],
  },
})
export class TrackingGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit
{
  private readonly logger = new Logger(TrackingGateway.name);

  // Track client connections by user type and ID
  private customerConnections = new Map<string, Set<string>>(); // customerId -> Set of socket IDs
  private driverConnections = new Map<string, Set<string>>(); // driverId -> Set of socket IDs

  // Track which deliveries each socket is subscribed to
  private socketDeliveries = new Map<string, Set<string>>(); // socketId -> Set of delivery IDs

  // Track delivery to customer mapping
  private deliveryCustomerMap = new Map<string, string>(); // deliveryId -> customerId

  // Track delivery to driver mapping
  private deliveryDriverMap = new Map<string, string>(); // deliveryId -> driverId

  // Store active delivery locations in memory for fast access
  private activeDeliveryLocations = new Map<string, DeliveryLocation>(); // deliveryId -> location data

  // Track driver activity (last update timestamp)
  private driverLastActivity = new Map<string, number>();

  @WebSocketServer()
  server: Server;

  constructor(
    private readonly prisma: PrismaService,
    private deliveryService: TrackingService,
  ) {}

  afterInit(server: Server) {
    server.use(SocketAuthMiddleware());
  }

  @Interval(60000) // Run every minute
  async checkDriverActivity() {
    const now = Date.now();
    const inactiveThreshold = 3 * 60 * 1000; // 3 minutes

    for (const [driverId, lastActivity] of this.driverLastActivity.entries()) {
      if (now - lastActivity > inactiveThreshold) {
        // Driver has been inactive for more than 3 minutes
        this.logger.warn(`Driver ${driverId} inactive for more than 3 minutes`);

        // Find all deliveries assigned to this driver
        for (const [
          deliveryId,
          driverId2,
        ] of this.deliveryDriverMap.entries()) {
          if (driverId === driverId2) {
            // Notify customer about driver inactivity
            const customerId = this.deliveryCustomerMap.get(deliveryId);
            if (customerId) {
              this.notifyCustomer(customerId, deliveryId, {
                type: 'driver_inactive',
                message: 'Driver location has not been updated recently',
                lastUpdate: new Date(lastActivity),
              });
            }
          }
        }
      }
    }
  }

  async handleConnection(client: Socket) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: client.data.user.sub },
        include: {
          adminDetails: true,
        },
      });

      if (!user) {
        throw new WsException('User not found');
      }

      // Store user info in socket data
      client.data.sub = user.id;
      client.data.role = user.role; // 'customer' or 'driver'

      this.logger.log(
        `${user.role} connected: ${user.id} (socket: ${client.id})`,
      );

      // Track connection based on user type
      if (user.role === UserRole.CUSTOMER) {
        if (!this.customerConnections.has(user.id)) {
          this.customerConnections.set(user.id, new Set());
        }
        this.customerConnections.get(user.id).add(client.id);
      } else if (user.role === UserRole.DRIVER) {
        if (!this.driverConnections.has(user.id)) {
          this.driverConnections.set(user.id, new Set());
        }
        this.driverConnections.get(user.id).add(client.id);

        // Update driver activity
        this.driverLastActivity.set(user.id, Date.now());
      }

      // Initialize delivery subscriptions for this socket
      this.socketDeliveries.set(client.id, new Set());

      // Send connection status confirmation
      client.emit('connectionStatus', {
        connected: true,
        userId: user.id,
        userType: user.role,
      });
    } catch (error) {
      this.logger.error(`Connection error:`, error);
      client.emit('connectionStatus', {
        connected: false,
        error: error.message,
      });
      client.disconnect(true);
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data?.sub;
    const userType = client.data?.role;

    if (
      userType === UserRole.CUSTOMER &&
      userId &&
      this.customerConnections.has(userId)
    ) {
      this.customerConnections.get(userId).delete(client.id);
      if (this.customerConnections.get(userId).size === 0) {
        this.customerConnections.delete(userId);
      }
    } else if (
      userType === UserRole.DRIVER &&
      userId &&
      this.driverConnections.has(userId)
    ) {
      this.driverConnections.get(userId).delete(client.id);
      if (this.driverConnections.get(userId).size === 0) {
        this.driverConnections.delete(userId);
      }
    }

    this.socketDeliveries.delete(client.id);
  }

  @SubscribeMessage('subscribeToDelivery')
  async handleSubscribeToDelivery(
    @ConnectedSocket() client: Socket,
    @MessageBody() body: any,
  ) {
    try {
      const userId = client.data?.sub;
      const userType = client.data?.role;
      const parsedData = JSON.parse(body);

      this.logger.log(
        `${userType} ${userId} subscribing to delivery ${parsedData.deliveryId}`,
      );

      if (!userId || !userType) {
        throw new WsException('User not authenticated');
      }

      // Get delivery details from service
      const delivery = await this.deliveryService.getDeliveryById(
        parsedData.deliveryId,
      );

      if (!delivery) {
        throw new WsException('Delivery not found');
      }

      // Verify user has access to this delivery
      if (
        userType === UserRole.CUSTOMER &&
        delivery.order.customer.id !== userId
      ) {
        throw new WsException('Access denied: not your delivery');
      } else if (
        userType === UserRole.DRIVER &&
        delivery.currentDriver.user.id !== userId
      ) {
        throw new WsException('Access denied: not your assigned delivery');
      }

      // Store delivery-customer/driver relationship
      this.deliveryCustomerMap.set(
        parsedData.deliveryId,
        delivery.order.customer.id,
      );
      if (delivery.currentLat && delivery.currentLong) {
        this.deliveryDriverMap.set(
          parsedData.deliveryId,
          delivery.currentDriver.user.id,
        );
      }

      // Add delivery to socket's subscriptions
      this.socketDeliveries.get(client.id).add(parsedData.deliveryId);

      this.logger.log(
        `${userType} ${userId} subscribed to delivery ${parsedData.deliveryId}`,
      );

      // If this is a customer and we have active location data, provide it immediately
      let locationData = null;
      if (
        userType === UserRole.CUSTOMER &&
        this.activeDeliveryLocations.has(parsedData.deliveryId)
      ) {
        locationData = this.activeDeliveryLocations.get(parsedData.deliveryId);
      }

      // Return initial delivery status
      return {
        event: 'deliveryStatus',
        data: {
          deliveryId: parsedData.deliveryId,
          status: delivery.status,
          currentLocation: {
            lat: delivery.currentLat,
            lng: delivery.currentLong,
          },
          locationHistory: locationData?.locationHistory || [],
          updatedAt: delivery.updatedAt,
          estimatedArrival: locationData?.estimatedArrival,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error subscribing to delivery ${body.deliveryId}:`,
        error,
      );
      client.emit('error', { message: error.message });
      return {
        event: 'error',
        data: error.message || 'Failed to subscribe to delivery',
      };
    }
  }

  @SubscribeMessage('unsubscribeFromDelivery')
  handleUnsubscribeFromDelivery(
    @ConnectedSocket() client: Socket,
    @MessageBody() deliveryId: string,
  ) {
    const deliveries = this.socketDeliveries.get(client.id);
    if (deliveries) {
      deliveries.delete(deliveryId);
    }

    return { event: 'unsubscribed', data: deliveryId };
  }

  // Enhanced location update method with additional metadata
  @SubscribeMessage('updateDeliveryLocation')
  async handleUpdateDeliveryLocation(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: any,
  ) {
    try {
      const userId = client.data?.sub;
      const userType = client.data?.role;
      const parsedData = JSON.parse(data);

      if (!userId || !userType) {
        throw new WsException('User not authenticated');
      }

      // Only drivers can update locations
      if (userType !== UserRole.DRIVER) {
        throw new WsException('Only drivers can update delivery location');
      }

      // Update driver activity timestamp
      this.driverLastActivity.set(userId, Date.now());

      const delivery = await this.deliveryService.getDeliveryById(
        parsedData.deliveryId,
      );
      if (!delivery) {
        throw new WsException('Delivery not found');
      }

      // Verify driver is assigned to this delivery
      if (delivery.currentDriver.user.id !== userId) {
        throw new WsException('Access denied: not your assigned delivery');
      }

      // Create location update with timestamp
      const locationUpdate: LocationUpdate = {
        lat: Number(parsedData.location.lat),
        lng: Number(parsedData.location.lng),
        timestamp: Date.now(),
      };

      // Update or create delivery location data in memory
      if (!this.activeDeliveryLocations.has(parsedData.deliveryId)) {
        this.activeDeliveryLocations.set(parsedData.deliveryId, {
          deliveryId: parsedData.deliveryId,
          driverId: userId,
          currentLocation: locationUpdate,
          locationHistory: [locationUpdate],
          updatedAt: new Date(),
        });
      } else {
        const locationData = this.activeDeliveryLocations.get(
          parsedData.deliveryId,
        );
        locationData.currentLocation = locationUpdate;

        // Keep last 20 location points for history
        locationData.locationHistory.push(locationUpdate);
        if (locationData.locationHistory.length > 20) {
          locationData.locationHistory.shift(); // Remove oldest
        }

        locationData.updatedAt = new Date();
      }

      // Update location in database (less frequently to reduce DB load)
      // We can use a debounce approach to only update every X seconds
      // but still keep real-time updates in memory
      await this.deliveryService.updateDeliveryLocation(parsedData.deliveryId, {
        lat: Number(parsedData.location.lat),
        lng: Number(parsedData.location.lng),
      });

      this.broadcastLocationUpdate(parsedData.deliveryId);

      return {
        event: 'locationUpdateAcknowledged',
        data: parsedData.deliveryId,
      };
    } catch (error) {
      this.logger.error(`Error updating location:`, error);
      return {
        event: 'error',
        data: error.message || 'Failed to update location',
      };
    }
  }

  // Driver updates delivery status
  @SubscribeMessage('updateDeliveryStatus')
  async handleUpdateDeliveryStatus(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: any,
  ) {
    try {
      const userId = client.data?.sub;
      const userType = client.data?.role;

      const parsedData = JSON.parse(data);

      if (!userId || !userType) {
        throw new WsException('User not authenticated');
      }

      // Only drivers can update delivery status
      if (userType !== UserRole.DRIVER) {
        throw new WsException('Only drivers can update delivery status');
      }

      // Update driver activity timestamp
      this.driverLastActivity.set(userId, Date.now());

      const delivery = await this.deliveryService.getDeliveryById(
        parsedData.deliveryId,
      );
      if (!delivery) {
        throw new WsException('Delivery not found');
      }

      // Verify driver is assigned to this delivery
      if (delivery.currentDriver.user.id !== userId) {
        throw new WsException('Access denied: not your assigned delivery');
      }

      // Update delivery status in the database
      await this.deliveryService.updateDeliveryStatus(
        parsedData.deliveryId,
        parsedData.status,
      );

      // If delivery is completed or failed, clean up from active tracking
      if (data.status === 'COMPLETED' || data.status === 'FAILED') {
        this.activeDeliveryLocations.delete(parsedData.deliveryId);
      }

      // Broadcast update to customer
      const customerId = this.deliveryCustomerMap.get(parsedData.deliveryId);
      if (customerId) {
        this.notifyCustomer(customerId, parsedData.deliveryId, {
          type: 'status_update',
          status: parsedData.status,
          message:
            parsedData.message ||
            `Delivery status updated to ${parsedData.status}`,
          timestamp: new Date(),
        });
      }

      return { event: 'statusUpdateAcknowledged', data: parsedData.deliveryId };
    } catch (error) {
      this.logger.error(`Error updating status:`, error);
      return {
        event: 'error',
        data: error.message || 'Failed to update status',
      };
    }
  }

  // Customer can send message to driver
  @SubscribeMessage('messageDriver')
  async handleCustomerMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: any,
  ) {
    try {
      const userId = client.data?.sub;
      const userType = client.data?.role;

      const parsedData = JSON.parse(data);

      if (!userId || !userType || userType !== UserRole.CUSTOMER) {
        throw new WsException('Unauthorized: Only customers can send messages');
      }

      const delivery = await this.deliveryService.getDeliveryById(
        parsedData.deliveryId,
      );
      if (!delivery || delivery.order.customer.id !== userId) {
        throw new WsException('Access denied: not your delivery');
      }

      const driverId = this.deliveryDriverMap.get(parsedData.deliveryId);
      if (!driverId) {
        throw new WsException('No driver assigned to this delivery yet');
      }

      // Send message to driver
      this.notifyDriver(driverId, parsedData.deliveryId, {
        type: 'customer_message',
        customerId: userId,
        message: parsedData.message,
        timestamp: new Date(),
      });

      return { event: 'messageSent', success: true };
    } catch (error) {
      this.logger.error(`Error sending message:`, error);
      return { event: 'error', data: error.message };
    }
  }

  // Driver can send message to customer
  @SubscribeMessage('messageCustomer')
  async handleDriverMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: any,
  ) {
    try {
      const userId = client.data?.sub;
      const userType = client.data?.role;

      const parsedData = JSON.parse(data);

      if (!userId || !userType || userType !== UserRole.DRIVER) {
        throw new WsException('Unauthorized: Only drivers can send messages');
      }

      const delivery = await this.deliveryService.getDeliveryById(
        parsedData.deliveryId,
      );
      if (!delivery || delivery.currentDriver.user.id !== userId) {
        throw new WsException('Access denied: not your delivery assignment');
      }

      const customerId = this.deliveryCustomerMap.get(parsedData.deliveryId);
      if (!customerId) {
        throw new WsException('Customer information not found');
      }

      // Send message to customer
      this.notifyCustomer(customerId, parsedData.deliveryId, {
        type: 'driver_message',
        driverId: userId,
        message: parsedData.message,
        timestamp: new Date(),
      });

      return { event: 'messageSent', success: true };
    } catch (error) {
      this.logger.error(`Error sending message:`, error);
      return { event: 'error', data: error.message };
    }
  }

  // Method to broadcast location updates to customer
  private broadcastLocationUpdate(deliveryId: string) {
    const locationData = this.activeDeliveryLocations.get(deliveryId);
    if (!locationData) return;

    const customerId = this.deliveryCustomerMap.get(deliveryId);
    if (!customerId) return;

    const payload = {
      type: 'location_update',
      deliveryId,
      currentLocation: locationData.currentLocation,
      updatedAt: locationData.updatedAt,
    };

    this.notifyCustomer(customerId, deliveryId, payload);
  }

  // Helper method to send notifications to a customer
  private notifyCustomer(customerId: string, deliveryId: string, payload: any) {
    if (this.customerConnections.has(customerId)) {
      const customerSockets = this.customerConnections.get(customerId);
      customerSockets.forEach((socketId) => {
        // Only send if this socket is subscribed to this delivery
        if (this.socketDeliveries.get(socketId)?.has(deliveryId)) {
          this.server.to(socketId).emit('deliveryUpdate', payload);
        }
      });
    }
  }

  // Helper method to send notifications to a driver
  private notifyDriver(driverId: string, deliveryId: string, payload: any) {
    if (this.driverConnections.has(driverId)) {
      const driverSockets = this.driverConnections.get(driverId);
      driverSockets.forEach((socketId) => {
        // Only send if this socket is subscribed to this delivery
        if (this.socketDeliveries.get(socketId)?.has(deliveryId)) {
          this.server.to(socketId).emit('deliveryMessage', payload);
        }
      });
    }
  }
}
