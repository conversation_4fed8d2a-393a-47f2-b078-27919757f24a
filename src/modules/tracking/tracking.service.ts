import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ErrandStatus } from '@prisma/client';
import { DeliveryRepository } from 'src/common/repositories';
@Injectable()
export class TrackingService {
  private readonly logger = new Logger(TrackingService.name);

  constructor(private readonly deliveryRepository: DeliveryRepository) {}

  async getDeliveryById(deliveryId: string) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) {
        return null;
      }

      return delivery;
    } catch (error) {
      this.logger.error(`Error fetching delivery ${deliveryId}:`, error);
      throw error;
    }
  }

  async getDeliveryStatus(deliveryId: string): Promise<string> {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      return delivery ? delivery.status : null;
    } catch (error) {
      this.logger.error(`Error fetching delivery status ${deliveryId}:`, error);
      throw error;
    }
  }

  async updateDeliveryLocation(
    deliveryId: string,
    location: { lat: number; lng: number },
  ): Promise<void> {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery) {
        throw new NotFoundException(`Delivery with ID ${deliveryId} not found`);
      }
      await this.deliveryRepository.updateCurrentLocation(
        deliveryId,
        location.lat,
        location.lng,
      );
    } catch (error) {
      this.logger.error(
        `Error updating delivery location ${deliveryId}:`,
        error,
      );
      throw error;
    }
  }

  async updateDeliveryStatus(deliveryId: string, status: ErrandStatus) {
    try {
      const delivery = await this.deliveryRepository.findOne(deliveryId);

      if (!delivery)
        throw new NotFoundException(`Delivery with ID ${deliveryId} not found`);

      const updatedDelivery = await this.deliveryRepository.updateStatus(
        deliveryId,
        status,
      );
      return updatedDelivery;
    } catch (error) {
      this.logger.error(`Error updating delivery status ${deliveryId}:`, error);
      throw error;
    }
  }
}
