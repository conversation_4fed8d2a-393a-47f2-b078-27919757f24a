import { JwtAuthGuard } from './../../common/guards/jwt-auth.guard';
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  UseGuards,
  Query,
  HttpStatus,
  HttpCode,
  Delete,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto, UpdateUserSwaggerDto } from './dto/update-user.dto';
import { UserResponseDto } from './users.entity';
import { IsAdminGuard } from 'src/common/guards/isAdmin.guard';
import { UpdateFirebaseTokenDto } from './dto/fbt.dto';
import { User } from 'src/common/decorators/user.decorator';
import { PaginationDto } from 'src/common/dto/pagination.dto';
@ApiTags('Users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: 201,
    description: 'The user has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - email or Google ID already in use',
  })
  @UseGuards(IsAdminGuard)
  @ApiBody({ type: CreateUserDto })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<any> {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @UseGuards(IsAdminGuard)
  @ApiQuery({ type: PaginationDto })
  async getAllUsers(@Query() pagination: PaginationDto): Promise<any> {
    return this.usersService.findAll(pagination);
  }

  @Get('drivers')
  @UseGuards(IsAdminGuard)
  @HttpCode(HttpStatus.OK)
  @ApiQuery({ type: PaginationDto })
  async getAllDrivers(@Query() pagination: PaginationDto): Promise<any> {
    return this.usersService.getAllDrivers(pagination);
  }
  @Get('customers')
  @UseGuards(IsAdminGuard)
  @HttpCode(HttpStatus.OK)
  @ApiQuery({ type: PaginationDto })
  async getAllCustomers(@Query() pagination: PaginationDto): Promise<any> {
    return this.usersService.getAllCustomers(pagination);
  }
  @Get('admins')
  @UseGuards(IsAdminGuard)
  @HttpCode(HttpStatus.OK)
  @ApiQuery({ type: PaginationDto })
  async getAllAdmins(@Query() pagination: PaginationDto): Promise<any> {
    return this.usersService.getAllAdmins(pagination);
  }

  @Get('trashed')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Get all trashed accounts' })
  async getTrashedAccounts(@Query() pagination: PaginationDto) {
    return this.usersService.getTrashedAccounts(pagination);
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the user to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @Patch('firebase-token')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update user Firebase token' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the user updating Firebase token',
  })
  @ApiResponse({
    status: 200,
    description: 'Firebase token updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input',
  })
  async updateFirebaseToken(
    @Body() updateFirebaseTokenDto: UpdateFirebaseTokenDto,
    @User() user,
  ) {
    return this.usersService.updateFirebaseToken(
      user.userId,
      updateFirebaseTokenDto,
    );
  }
  @Patch(':id')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Update a user by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the user to update',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: UserResponseDto,
  })
  @ApiBody({ type: UpdateUserSwaggerDto })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<any> {
    return this.usersService.update(id, updateUserDto);
  }
  @UseGuards(JwtAuthGuard, IsAdminGuard)
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the user to delete',
  })
  @ApiResponse({ status: 204, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async deleteUser(@Param('id') id: string) {
    return this.usersService.hardDeleteUser(id);
  }

  @Patch('restore/:id')
  @UseGuards(IsAdminGuard)
  @ApiOperation({ summary: 'Restore a user by ID' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'ID of the user to restore',
  })
  @ApiResponse({ status: 200, description: 'User restored successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async restoreUser(@Param('id') id: string) {
    return this.usersService.restoreUser(id);
  }
}
