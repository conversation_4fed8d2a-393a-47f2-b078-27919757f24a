import { ApiProperty } from '@nestjs/swagger';

import { AddressResponseDto } from '../address/address.entity';
import { ProfileResponseDto } from '../profile/profile.entity';

export class UserResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  fullname: string;

  @ApiProperty()
  phone?: string;

  @ApiProperty()
  isVerified: boolean;

  @ApiProperty({ type: ProfileResponseDto, nullable: true })
  profile?: ProfileResponseDto;

  @ApiProperty({ type: [AddressResponseDto] })
  address: AddressResponseDto[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
