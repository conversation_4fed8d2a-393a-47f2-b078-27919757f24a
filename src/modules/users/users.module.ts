import { Module } from '@nestjs/common';

import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { UsersRepository } from 'src/common/repositories';
import { OtpRepository } from 'src/common/repositories/otp.repository';

@Module({
  controllers: [UsersController],
  providers: [UsersService, UsersRepository, OtpRepository],
  exports: [UsersService],
})
export class UsersModule {}
