import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserRole, OTPType } from '@prisma/client';
import { handlePrismaError } from 'src/common/utils/handle-prisma-exceptions';
import { CreateGoogleUserDto } from './dto/create-google-user.dto';
import * as bcrypt from 'bcrypt';
import { UpdateFirebaseTokenDto } from './dto/fbt.dto';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import { EmailService } from 'src/core/email/email.service';
import { UsersRepository, OtpRepository } from 'src/common/repositories';
import { Users } from 'src/common/schemas';
import { PaginatedResult } from 'src/common/interfaces';

@Injectable()
export class UsersService {
  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly emailService: EmailService,
    private readonly otpRepository: OtpRepository,
  ) {}
  async create(createUserDto: CreateUserDto): Promise<Users> {
    const { email, password, role, fullname } = createUserDto;
    try {
      const existingUserByEmail =
        await this.usersRepository.getUserByEmail(email);

      if (existingUserByEmail && existingUserByEmail.isDeleted) {
        // await this.prisma.user.update({
        //   where: { id: existingUserByEmail.id },
        //   data: { isDeleted: false, deletedAt: null },
        // });

        throw new BadRequestException(
          'Account was deleted. Please contact support',
        );
      }

      const hash = await bcrypt.hash(password, 10);
      if (existingUserByEmail)
        throw new BadRequestException('Email already in use');
      const user = await this.usersRepository.create({
        email,
        password: hash,
        fullname,
        role,
        profile: {
          create: {
            gender: null,
            avatar: 'https://avatar.iran.liara.run/public/50',
            nickname: null,
          },
        },
      });
      return user;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findAll(pagination: PaginationDto): Promise<PaginatedResult<Users>> {
    try {
      return this.usersRepository.findAll(pagination);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async findOne(id: string) {
    try {
      const user = await this.usersRepository.findOne(id);
      if (!user) {
        throw new NotFoundException(`User with id ${id} not found`);
      }
      return user;
    } catch (err) {
      handlePrismaError(err);
    }
  }

  async getUserByEmail(email: string): Promise<Users> {
    try {
      const user = await this.usersRepository.getUserByEmail(email);
      if (!user)
        throw new NotFoundException(`User with email ${email} not found`);
      return user;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getUserByPhone(phone: string): Promise<Users> {
    try {
      const user = await this.usersRepository.getUserByPhone(phone);
      if (!user)
        throw new NotFoundException(`User with phone ${phone} not found`);

      return user;
    } catch (err) {
      handlePrismaError(err);
    }
  }

  async addGoogleUser(dto: CreateGoogleUserDto): Promise<Users> {
    const { googleId, email, avatar, fullname } = dto;

    try {
      const existingEmailUser =
        await this.usersRepository.getUserByEmail(email);

      if (existingEmailUser)
        throw new ConflictException('Email already in use');
      const user = await this.usersRepository.addGoogleUser({
        email,
        googleId,
        fullname,
        role: UserRole.CUSTOMER,
        isEmailVerified: dto.isEmailVerified,
        profile: {
          create: {
            gender: null,
            avatar: avatar,
            nickname: null,
          },
        },
      });

      return user;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<Users> {
    try {
      const { email, role, fullname } = updateUserDto;

      const existingUser = await this.usersRepository.findOne(id);
      const userByEmail = await this.usersRepository.getUserByEmail(email);

      if (!existingUser)
        throw new NotFoundException(`User with id ${id} not found`);

      if (userByEmail && userByEmail.id !== id)
        throw new ConflictException('Email already in use');

      const updatedUser = await this.usersRepository.update(id, {
        email,
        role,
        fullname,
      });

      if (!updatedUser) {
        throw new NotFoundException(`User with id ${id} not found`);
      }
      return updatedUser;
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async updateFirebaseToken(
    id: string,
    updateFirebaseTokenDto: UpdateFirebaseTokenDto,
  ) {
    try {
      const user = await this.usersRepository.findOne(id);
      if (!user) throw new NotFoundException('User not found');

      return await this.usersRepository.updateFirebaseToken(id, {
        fcmToken: updateFirebaseTokenDto.firebaseToken,
      });
    } catch (error) {
      handlePrismaError(error);
    }
  }
  async getAllDrivers(
    pagination: PaginationDto,
  ): Promise<PaginatedResult<Users>> {
    return this.usersRepository.findAllDrivers(pagination);
  }

  async getAllCustomers(
    pagination: PaginationDto,
  ): Promise<PaginatedResult<Users>> {
    return this.usersRepository.findAllCustomers(pagination);
  }

  async getAllAdmins(
    pagination: PaginationDto,
  ): Promise<PaginatedResult<Users>> {
    return this.usersRepository.findAllAdmins(pagination);
  }

  async hardDeleteUser(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user)
        throw new NotFoundException(`User with ID ${userId} not found`);
      await this.usersRepository.remove(userId);
    } catch (error) {
      handlePrismaError(error);
    }

    return { message: 'User and all related data deleted successfully.' };
  }

  async softDeleteUser(userId: string, token: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user)
        throw new NotFoundException(`User with ID ${userId} not found`);

      if (user.isDeleted) throw new BadRequestException('User already deleted');

      const otpRecord = await this.otpRepository.findOne(token);

      if (!otpRecord) {
        throw new BadRequestException('OTP not found');
      }

      if (otpRecord.isUsed) {
        throw new BadRequestException('OTP already used');
      }

      if (new Date() > otpRecord.expiresAt) {
        throw new BadRequestException('OTP has expired');
      }

      await this.otpRepository.remove(otpRecord.id);

      await this.usersRepository.softDelete(userId);
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async sendDeleteRequest(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user)
        throw new NotFoundException(`User with ID ${userId} not found`);

      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const token = await this.otpRepository.create({
        userId,
        code: otp,
        type: OTPType.ACCOUNT_DELETION,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000),
        isUsed: false,
      });
      // send email
      await this.emailService.sendUserOtp(
        user.email,
        user.fullname,
        token.code,
        'Hello Your OTP for Account Deletion',
        'To delete your account, please use the One-Time Password (OTP) below.',
      );

      return { message: 'OTP sent successfully.' };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async restoreUser(userId: string) {
    try {
      const user = await this.usersRepository.findOne(userId);
      if (!user)
        throw new NotFoundException(`User with ID ${userId} not found`);

      await this.usersRepository.restore(userId);
      return { message: 'User restored successfully.' };
    } catch (error) {
      handlePrismaError(error);
    }
  }

  async getTrashedAccounts(
    pagination: PaginationDto,
  ): Promise<PaginatedResult<Users>> {
    try {
      console.log('====================================');
      console.log(pagination);
      console.log('====================================');
      const users = await this.usersRepository.getTrashedAccounts(pagination);
      return users;
    } catch (error) {
      handlePrismaError(error);
    }
  }
}
